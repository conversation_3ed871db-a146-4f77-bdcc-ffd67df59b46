# 🕷️ Manus Crawler - Restructured

Ứng dụng FastAPI để crawl dữ liệu từ trang web Manus.im với cấu trúc code được tổ chức lại chuyên nghiệp.

## ✨ Tính năng chính

- 🌐 **Web Scraping**: Crawl dữ liệu từ Manus.im với Playwright
- 🚀 **FastAPI Backend**: RESTful API với WebSocket support
- 🔄 **Realtime Updates**: Cập nhật tiến trình crawl qua WebSocket
- 🎭 **Chrome Profile Management**: Quản lý multiple Chrome profiles
- 📊 **Admin Panel**: Giao diện quản trị web-based
- 🏷️ **Message Classification**: Tự động phân loại message types (Text, Code, List, File, Mixed)
- 🐳 **Docker Support**: Containerization hoàn chỉnh
- 🧪 **Comprehensive Testing**: Unit tests và integration tests

## 📁 Cấu trúc dự án mới

```
manus_crawler/
├── 📁 app/                          # Main application code
│   ├── __init__.py
│   ├── main.py                      # FastAPI app entry point
│   ├── 📁 api/                      # API routes
│   │   ├── __init__.py
│   │   ├── endpoints.py             # API endpoints
│   │   └── websocket.py             # WebSocket handlers
│   ├── 📁 core/                     # Core functionality
│   │   ├── __init__.py
│   │   ├── config.py                # Configuration
│   │   ├── crawler.py               # Crawler logic
│   │   └── selectors.py             # CSS selectors
│   ├── 📁 models/                   # Pydantic models
│   │   ├── __init__.py
│   │   └── schemas.py               # Request/Response models
│   └── 📁 static/                   # Static files
│       └── 📁 templates/            # HTML templates
│           ├── index.html
│           └── admin.html
├── 📁 tests/                        # Test files
│   ├── __init__.py
│   ├── test_api.py
│   ├── test_crawler.py
│   └── test_admin.py
├── 📁 scripts/                      # Utility scripts
│   ├── setup.sh
│   ├── setup.bat
│   └── demo_html_parser.py
├── 📁 docs/                         # Documentation
│   ├── README.md
│   ├── QUICKSTART.md
│   ├── ADMIN_PANEL_COMPLETE.md
│   └── SINGLETON_LOCK_FIX.md
├── 📁 data/                         # Data files
│   ├── html-manus.example.html
│   └── chrome_profiles/             # Chrome profiles
├── 📁 docker/                       # Docker files
│   ├── Dockerfile
│   └── docker-compose.yml
├── .env                             # Environment variables
├── .gitignore
├── requirements.txt
└── run.py                           # Application runner
```

## 🚀 Quick Start

### Method 1: Using the new structure
```bash
# Install dependencies
pip install -r requirements.txt
playwright install chromium

# Run the application
python run.py
```

### Method 2: Using scripts
```bash
# Linux/macOS
./scripts/setup.sh

# Windows
scripts/setup.bat
```

### Method 3: Using Docker
```bash
cd docker
docker-compose up --build
```

## 🎯 Access Points

- **Main Interface**: http://localhost:8000/ui
- **Admin Panel**: http://localhost:8000/admin
- **API Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health

## 🔧 Configuration

Environment variables in `.env`:
```env
ADMIN_API_KEY=your_super_secret_key_here
CHROME_PROFILE_BASE_PATH=./data/chrome_profiles
DEBUG=false
HOST=0.0.0.0
PORT=8000
```

## 🧪 Testing

```bash
# Run all tests
python -m pytest tests/

# Run specific test
python tests/test_api.py

# Test message classification
./venv/bin/python tests/test_message_classifier.py
./venv/bin/python tests/test_real_html_classification.py

# Demo message classification
./venv/bin/python scripts/demo_message_types.py
```

## 📚 Documentation

Detailed documentation is available in the `docs/` directory:
- `QUICKSTART.md` - Quick start guide
- `ADMIN_PANEL_COMPLETE.md` - Admin panel documentation
- `MESSAGE_CLASSIFICATION.md` - Message classification system guide
- `SINGLETON_LOCK_FIX.md` - Technical fixes documentation

## 🎉 Benefits of New Structure

✅ **Modular Design** - Separated concerns into logical modules
✅ **Scalable Architecture** - Easy to add new features
✅ **Better Testing** - Organized test structure
✅ **Clean Configuration** - Centralized settings
✅ **Professional Layout** - Industry standard structure
✅ **Easy Deployment** - Docker support with proper paths

## 🔄 Migration from Old Structure

The application has been completely restructured for better maintainability:

- **Old**: All files in root directory
- **New**: Organized into logical packages
- **Benefits**: Better imports, easier testing, cleaner code

All functionality remains the same, just better organized! 🎯
