"""
Module phân loại các loại message từ Manus AI
"""

import re
from typing import Dict, Any, Optional, List
from bs4 import BeautifulSoup


class ManusMessageClassifier:
    """
    Phân loại các loại message từ Manus dựa trên nội dung HTML và text.
    """
    
    # Patterns để nhận diện các loại nội dung
    CODE_PATTERNS = [
        r'<code[^>]*>.*?</code>',
        r'<pre[^>]*>.*?</pre>',
        r'```[\s\S]*?```',
        r'`[^`]+`',
        r'class\s+\w+\s*\(',
        r'def\s+\w+\s*\(',
        r'function\s+\w+\s*\(',
        r'import\s+\w+',
        r'from\s+\w+\s+import',
        r'console\.log\s*\(',
        r'print\s*\(',
        r'async\s+def\s+\w+',
        r'await\s+\w+',
    ]
    
    FILE_PATTERNS = [
        r'\.py\b',
        r'\.js\b',
        r'\.html\b',
        r'\.css\b',
        r'\.json\b',
        r'\.md\b',
        r'\.txt\b',
        r'\.yml\b',
        r'\.yaml\b',
        r'\.xml\b',
        r'\.sql\b',
        r'\.sh\b',
        r'\.bat\b',
        r'\.dockerfile\b',
        r'requirements\.txt',
        r'package\.json',
        r'docker-compose\.yml',
    ]
    
    LIST_PATTERNS = [
        r'<ul[^>]*>.*?</ul>',
        r'<ol[^>]*>.*?</ol>',
        r'<li[^>]*>.*?</li>',
        r'^\s*[-*+]\s+',  # Markdown list
        r'^\s*\d+\.\s+',  # Numbered list
    ]
    
    STEP_PATTERNS = [
        r'step\s+\d+',
        r'bước\s+\d+',
        r'phase\s+\d+',
        r'giai\s+đoạn\s+\d+',
        r'first[ly]?\s*[,:]',
        r'second[ly]?\s*[,:]',
        r'third[ly]?\s*[,:]',
        r'finally\s*[,:]',
        r'đầu\s+tiên\s*[,:]',
        r'tiếp\s+theo\s*[,:]',
        r'cuối\s+cùng\s*[,:]',
    ]

    @classmethod
    def analyze_html_content(cls, html_content: str) -> Dict[str, Any]:
        """
        Phân tích nội dung HTML để xác định các thành phần.
        
        Args:
            html_content: Nội dung HTML của message
            
        Returns:
            Dictionary chứa thông tin phân tích
        """
        if not html_content:
            return {"has_content": False}
            
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            text_content = soup.get_text().strip()
            
            analysis = {
                "has_content": bool(text_content),
                "text_length": len(text_content),
                "html_length": len(html_content),
                "has_code_blocks": False,
                "has_lists": False,
                "has_headings": False,
                "has_links": False,
                "has_images": False,
                "code_blocks_count": 0,
                "list_items_count": 0,
                "headings_count": 0,
                "links_count": 0,
                "images_count": 0,
                "detected_languages": [],
                "file_references": [],
                "step_indicators": [],
            }
            
            # Kiểm tra code blocks
            code_elements = soup.find_all(['code', 'pre'])
            analysis["code_blocks_count"] = len(code_elements)
            analysis["has_code_blocks"] = len(code_elements) > 0
            
            # Phát hiện ngôn ngữ lập trình
            for code_elem in code_elements:
                code_text = code_elem.get_text()
                detected_lang = cls._detect_programming_language(code_text)
                if detected_lang and detected_lang not in analysis["detected_languages"]:
                    analysis["detected_languages"].append(detected_lang)
            
            # Kiểm tra lists
            list_elements = soup.find_all(['ul', 'ol', 'li'])
            analysis["list_items_count"] = len(soup.find_all('li'))
            analysis["has_lists"] = len(list_elements) > 0
            
            # Kiểm tra headings
            heading_elements = soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
            analysis["headings_count"] = len(heading_elements)
            analysis["has_headings"] = len(heading_elements) > 0
            
            # Kiểm tra links
            link_elements = soup.find_all('a')
            analysis["links_count"] = len(link_elements)
            analysis["has_links"] = len(link_elements) > 0
            
            # Kiểm tra images
            img_elements = soup.find_all('img')
            analysis["images_count"] = len(img_elements)
            analysis["has_images"] = len(img_elements) > 0
            
            # Tìm file references
            analysis["file_references"] = cls._find_file_references(text_content)
            
            # Tìm step indicators
            analysis["step_indicators"] = cls._find_step_indicators(text_content)
            
            return analysis
            
        except Exception as e:
            return {
                "has_content": False,
                "error": str(e)
            }

    @classmethod
    def _detect_programming_language(cls, code_text: str) -> Optional[str]:
        """Phát hiện ngôn ngữ lập trình từ code text."""
        code_lower = code_text.lower().strip()
        
        # Python
        if any(keyword in code_lower for keyword in ['def ', 'import ', 'from ', 'async def', 'await ', 'print(']):
            return "python"
        
        # JavaScript
        if any(keyword in code_lower for keyword in ['function ', 'const ', 'let ', 'var ', 'console.log', '=>']):
            return "javascript"
        
        # HTML
        if any(keyword in code_lower for keyword in ['<html', '<div', '<span', '<body', '<!doctype']):
            return "html"
        
        # CSS
        if any(keyword in code_lower for keyword in ['{', '}', ':', ';']) and any(prop in code_lower for prop in ['color', 'background', 'margin', 'padding']):
            return "css"
        
        # SQL
        if any(keyword in code_lower for keyword in ['select ', 'from ', 'where ', 'insert ', 'update ', 'delete ']):
            return "sql"
        
        # Shell/Bash
        if any(keyword in code_lower for keyword in ['#!/bin/', 'echo ', 'cd ', 'ls ', 'mkdir ', 'chmod ']):
            return "shell"
        
        return None

    @classmethod
    def _find_file_references(cls, text: str) -> List[str]:
        """Tìm các tham chiếu đến file trong text."""
        file_refs = []
        for pattern in cls.FILE_PATTERNS:
            matches = re.findall(pattern, text, re.IGNORECASE)
            file_refs.extend(matches)
        return list(set(file_refs))  # Remove duplicates

    @classmethod
    def _find_step_indicators(cls, text: str) -> List[str]:
        """Tìm các chỉ dẫn bước trong text."""
        step_indicators = []
        for pattern in cls.STEP_PATTERNS:
            matches = re.findall(pattern, text, re.IGNORECASE | re.MULTILINE)
            step_indicators.extend(matches)
        return list(set(step_indicators))

    @classmethod
    def classify_message_type(cls, manus_html: str, manus_text: str) -> Dict[str, Any]:
        """
        Phân loại loại message dựa trên HTML và text content.
        
        Args:
            manus_html: Nội dung HTML của message
            manus_text: Nội dung text của message
            
        Returns:
            Dictionary chứa thông tin phân loại
        """
        if not manus_html and not manus_text:
            return {
                "message_subtype": "empty",
                "confidence": 1.0,
                "reasoning": "No content provided"
            }
        
        # Phân tích HTML content
        html_analysis = cls.analyze_html_content(manus_html) if manus_html else {}
        
        # Phân tích text content
        text_lower = manus_text.lower() if manus_text else ""
        
        # Logic phân loại
        classification = {
            "message_subtype": "text",  # Default
            "confidence": 0.5,
            "reasoning": "Default text classification",
            "content_analysis": html_analysis
        }
        
        # Kiểm tra code content
        if html_analysis.get("has_code_blocks", False) or any(re.search(pattern, text_lower, re.IGNORECASE) for pattern in cls.CODE_PATTERNS):
            if html_analysis.get("code_blocks_count", 0) >= 3 or len(html_analysis.get("detected_languages", [])) >= 2:
                classification.update({
                    "message_subtype": "mixed",
                    "confidence": 0.9,
                    "reasoning": "Multiple code blocks or languages detected"
                })
            else:
                classification.update({
                    "message_subtype": "code",
                    "confidence": 0.8,
                    "reasoning": "Code blocks or programming patterns detected"
                })
        
        # Kiểm tra file content
        elif len(html_analysis.get("file_references", [])) >= 2:
            classification.update({
                "message_subtype": "file",
                "confidence": 0.8,
                "reasoning": "Multiple file references detected"
            })
        
        # Kiểm tra list/steps content
        elif html_analysis.get("has_lists", False) or len(html_analysis.get("step_indicators", [])) >= 2:
            classification.update({
                "message_subtype": "list",
                "confidence": 0.7,
                "reasoning": "Lists or step indicators detected"
            })
        
        # Kiểm tra mixed content
        elif (html_analysis.get("has_headings", False) and 
              html_analysis.get("has_lists", False) and 
              html_analysis.get("text_length", 0) > 500):
            classification.update({
                "message_subtype": "mixed",
                "confidence": 0.8,
                "reasoning": "Multiple content types detected (headings, lists, long text)"
            })
        
        # Pure text
        else:
            classification.update({
                "message_subtype": "text",
                "confidence": 0.9,
                "reasoning": "Simple text content without special patterns"
            })
        
        return classification
