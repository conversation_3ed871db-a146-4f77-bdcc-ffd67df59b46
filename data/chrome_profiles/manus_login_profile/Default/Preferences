{"accessibility": {"captions": {"live_caption_language": "en-US"}}, "account_tracker_service_last_update": "*****************", "ack_existing_ntp_extensions": true, "alternate_error_pages": {"backup": true}, "apps": {"shortcuts_arch": "arm64", "shortcuts_version": 7}, "autocomplete": {"retention_policy_last_version": 136}, "autofill": {"last_version_deduped": 136}, "browser": {"has_seen_welcome_page": false, "window_placement": {"bottom": 906, "left": 22, "maximized": false, "right": 1304, "top": 60, "work_area_bottom": 982, "work_area_left": 0, "work_area_right": 1512, "work_area_top": 38}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 22094, "default_search_provider": {"guid": ""}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "enterprise_profile_guid": "feb0a1c1-44e7-4473-9dad-8a681460f18b", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "136.0.7103.25"}, "gaia_cookie": {"changed_time": **********.901283, "hash": "DdKtCN+bu2sFZObU5DLJqSLRgNM=", "last_list_accounts_data": "[\"gaia.l.a.r\",[[\"gaia.l.a\",1,\"A Le Thanh Son\",\"<EMAIL>\",\"https://lh3.googleusercontent.com/-6uAClGXzp2I/AAAAAAAAAAI/AAAAAAAAAAA/Jy05hUWv1rI/s48-c/photo.jpg\",1,1,0,null,1,\"108257609070626939588\",null,null,null,null,1]]]"}, "gcm": {"product_category_for_subtypes": "org.chromium.macosx"}, "google": {"services": {"signin_scoped_device_id": "7dbd4f7f-fb12-414c-bb76-fdbdb093ec8b"}}, "in_product_help": {"new_badge": {"Compose": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeProactiveNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "LensOverlay": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}}, "recent_session_enabled_time": "*****************", "recent_session_start_times": ["*****************", "*****************"], "session_last_active_time": "**********3269891", "session_start_time": "*****************"}, "intl": {"selected_languages": "en-US,en"}, "invalidation": {"per_sender_topics_to_handler": {"1013309121859": {}}}, "media": {"device_id_salt": "3476F296141016113298B27800391F6E", "engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "IN4QQWggVMdxVpEOHCQvMrNR5RfVV+lti0Ft4XysqZnfnuPjWaE3UxfuTKAFyxo+OavoW3mjXYly6x7BzZ7Qjg=="}, "ntp": {"num_personal_suggestions": 4}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "HISTORY_CLUSTERS": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PRICE_TRACKING": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false, "relaunch_chrome_bubble_dismissed_counter": 0}, "privacy_sandbox": {"fake_notice": {"prompt_shown_time": "*****************", "prompt_shown_time_sync": "*****************"}, "first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 26, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {"https://[*.]google.com,https://[*.]manus.im": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*********", "setting": 1}}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {"https://accounts.google.com.vn:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 13, 14, 16, 23, 25, 29]}}, "https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 13, 14, 16, 23, 25, 29]}}, "https://accounts.youtube.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 13, 14, 16, 23, 25, 29]}}}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"https://[*.]google.com,*": {"last_modified": "*****************", "setting": {}}, "https://[*.]google.com.vn,*": {"last_modified": "*****************", "setting": {}}, "https://[*.]manus.im,*": {"last_modified": "**********4060718", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "**********3766111", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": true}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"https://accounts.google.com.vn:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 5}}, "https://accounts.google.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 8}}, "https://manus.im:443,*": {"expiration": "*****************", "last_modified": "**********8317657", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 21}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3392702938125308e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 2.7, "rawScore": 4.****************}}, "https://manus.im:443,*": {"last_modified": "**********4063007", "setting": {"lastEngagementTime": 1.3392703094062976e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 15.0, "rawScore": 23.***************}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "136.0.7103.25", "creation_time": "13392661524541829", "did_work_around_bug_364820109_default": true, "did_work_around_bug_364820109_exceptions": true, "exit_type": "Normal", "family_member_role": "not_in_family", "last_engagement_time": "**********4062976", "last_time_obsolete_http_credentials_removed": 1748225719.431041, "last_time_password_store_metrics_reported": 1748187954.574013, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "Your Chromium", "password_hash_data_list": [{"hash": "djEwBGK3vG94kSZq8iRheaoELQ==", "is_gaia": "djEwlYECCo/zlDoLthwIKSFM0A==", "last_signin": 1748229345.740279, "salt_length": "djEwOXvsEJ5e7CDjSjLjCDQWlh5wus4fhz93Jyn+OUsQVbI=", "username": "djEwShy11AtM566FSb5f5UdO4knognrl5KeAnKGQgPT4D5c="}], "were_old_google_logins_removed": true}, "safebrowsing": {"event_timestamps": {}, "metrics_last_log_time": "13392661524", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "segmentation_platform": {"client_result_prefs": "ClIKDXNob3BwaW5nX3VzZXISQQo2DQAAAAAQxqLziOCR5RcaJAocChoNAAAAPxIMU2hvcHBpbmdVc2VyGgVPdGhlchIEEAIYBCADENqi84jgkeUX", "device_switcher_util": {"result": {"labels": ["NotSynced"]}}, "last_db_compaction_time": "13392604799000000", "uma_in_sql_start_time": "13392661524583032"}, "sessions": {"event_log": [{"crashed": false, "time": "13392699374706580", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13392699379946329", "type": 2, "window_count": 1}, {"crashed": false, "time": "13392701599333956", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13392701604621430", "type": 2, "window_count": 1}, {"crashed": false, "time": "13392702194460761", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13392702199644133", "type": 2, "window_count": 1}, {"crashed": false, "time": "13392702509368338", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13392702517703894", "type": 2, "window_count": 1}, {"crashed": false, "time": "13392702542734274", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13392702603563604", "type": 2, "window_count": 1}, {"crashed": false, "time": "13392702615765506", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13392702621616282", "type": 2, "window_count": 1}, {"crashed": false, "time": "13392702910627202", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13392702916225378", "type": 2, "window_count": 1}, {"crashed": false, "time": "13392702922577409", "type": 0}, {"crashed": true, "time": "13392702958691779", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13392702964277420", "type": 2, "window_count": 1}, {"crashed": false, "time": "**********3258394", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "**********8312498", "type": 2, "window_count": 1}], "session_data_status": 5}, "settings": {"force_google_safesearch": false}, "signin": {"allowed": false, "cookie_clear_on_exit_migration_notice_complete": true}, "spellcheck": {"dictionaries": ["en-GB"]}, "sync": {"data_type_status_for_sync_to_signin": {"app_list": false, "app_settings": false, "apps": false, "arc_package": false, "autofill": false, "autofill_profiles": false, "autofill_valuable": false, "autofill_wallet": false, "autofill_wallet_credential": false, "autofill_wallet_metadata": false, "autofill_wallet_offer": false, "autofill_wallet_usage": false, "bookmarks": false, "collaboration_group": false, "contact_info": false, "cookies": false, "device_info": false, "dictionary": false, "extension_settings": false, "extensions": false, "history": false, "history_delete_directives": false, "incoming_password_sharing_invitation": false, "managed_user_settings": false, "nigori": false, "os_preferences": false, "os_priority_preferences": false, "outgoing_password_sharing_invitation": false, "passwords": false, "plus_address": false, "plus_address_setting": false, "power_bookmark": false, "preferences": false, "printers": false, "printers_authorization_servers": false, "priority_preferences": false, "product_comparison": false, "reading_list": false, "saved_tab_group": false, "search_engines": false, "security_events": false, "send_tab_to_self": false, "sessions": false, "shared_tab_group_account_data": false, "shared_tab_group_data": false, "sharing_message": false, "themes": false, "user_consent": false, "user_events": false, "web_apps": false, "webapks": false, "webauthn_credential": false, "wifi_configurations": false, "workspace_desk": false}, "encryption_bootstrap_token_per_account_migration_done": true, "feature_status_for_sync_to_signin": 5, "passwords_per_account_pref_migration_done": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "tab_group_saves_ui_update_migrated": true, "toolbar": {"pinned_chrome_labs_migration_complete": true}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "136"}, "webauthn": {"touchid": {"metadata_secret": "1HG2b1HMP3YYyjNlapPL+onDZMbs3ihdhjHHcIWhAc8="}}}