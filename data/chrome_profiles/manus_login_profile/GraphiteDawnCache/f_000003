IB      
                    #ifdef __clang__
                    #pragma clang diagnostic ignored "-Wall"
                    #endif
                #include <metal_stdlib>
using namespace metal;

template<typename T, size_t N>
struct tint_array {
  const constant T& operator[](size_t i) const constant { return elements[i]; }
  device T& operator[](size_t i) device { return elements[i]; }
  const device T& operator[](size_t i) const device { return elements[i]; }
  thread T& operator[](size_t i) thread { return elements[i]; }
  const thread T& operator[](size_t i) const thread { return elements[i]; }
  threadgroup T& operator[](size_t i) threadgroup { return elements[i]; }
  const threadgroup T& operator[](size_t i) const threadgroup { return elements[i]; }
  T elements[N];
};

struct tint_struct {
  float4 tint_member;
  uint2 tint_member_1;
  float2 tint_member_2;
  float4 tint_member_3;
  float2 tint_member_4;
  float tint_member_5;
};

struct tint_struct_1 {
  float4 tint_member_6;
};

struct tint_struct_4 {
  /* 0x0000 */ float4x4 tint_member_9;
  /* 0x0040 */ tint_array<float4, 4> tint_member_10;
  /* 0x0080 */ float4 tint_member_11;
  /* 0x0090 */ float tint_member_12;
  /* 0x0094 */ float tint_member_13;
  /* 0x0098 */ float tint_member_14;
  /* 0x009c */ float tint_member_15;
  /* 0x00a0 */ int tint_member_16;
  /* 0x00a4 */ int tint_member_17;
  /* 0x00a8 */ int tint_member_18;
  /* 0x00ac */ tint_array<int8_t, 4> tint_pad;
  /* 0x00b0 */ float2 tint_member_19;
  /* 0x00b8 */ tint_array<int8_t, 8> tint_pad_1;
  /* 0x00c0 */ float4 tint_member_20;
  /* 0x00d0 */ float4 tint_member_21;
  /* 0x00e0 */ float tint_member_22;
  /* 0x00e4 */ tint_array<int8_t, 12> tint_pad_2;
};

struct tint_struct_3 {
  /* 0x0000 */ tint_array<tint_struct_4, 1> tint_member_8;
};

struct tint_struct_2 {
  const device tint_struct_3* tint_member_7;
  thread uint* tint_member_23;
  sampler tint_member_24;
  texture2d<float, access::sample> tint_member_25;
  sampler tint_member_26;
  texture2d<float, access::sample> tint_member_27;
};

struct tint_struct_5 {
  float4 tint_member_28 [[color(0)]];
};

struct tint_struct_6 {
  uint2 tint_member_29 [[user(locn0)]] [[flat]];
  float2 tint_member_30 [[user(locn1)]];
  float4 tint_member_31 [[user(locn2)]];
  float2 tint_member_32 [[user(locn3)]];
  float tint_member_33 [[user(locn4)]];
};

float3 v(float3 v_1) {
  float const v_2 = (v_1.x * 0.01745329238474369049f);
  float const v_3 = cos(v_2);
  float const v_4 = (v_1.x * 0.01745329238474369049f);
  float const v_5 = sin(v_4);
  return float3(v_1.z, (v_1.y * v_3), (v_1.y * v_5));
}

float3 v_6(float3 v_7) {
  float3 v_8 = v_7;
  float const v_9 = v_8.x;
  float const v_10 = (v_9 - (360.0f * floor((v_9 / 360.0f))));
  v_8.x = v_10;
  if ((v_8.x < 0.0f)) {
    v_8.x = (v_8.x + 360.0f);
  }
  v_8 = float3(v_8.x, (v_8.yz * 0.00999999977648258209f));
  float3 const v_11 = (float3(0.0f, 8.0f, 4.0f) + (v_8.x * 0.03333333507180213928f));
  float3 const v_12 = (v_11 - (12.0f * floor((v_11 / 12.0f))));
  float3 const v_13 = v_12;
  float const v_14 = min(v_8.z, (1.0f - v_8.z));
  float const v_15 = (v_8.y * v_14);
  float3 const v_16 = min((v_13 - 3.0f), (9.0f - v_13));
  float3 const v_17 = clamp(v_16, float3(-1.0f), float3(1.0f));
  return (v_8.z - (v_15 * v_17));
}

float3 v_18(float3 v_19) {
  float3 v_20 = 0.0f;
  v_20.y = ((v_19.x + 16.0f) * 0.00862068962305784225f);
  v_20.x = ((v_19.y * 0.00200000009499490261f) + v_20.y);
  v_20.z = (v_20.y - (v_19.z * 0.00499999988824129105f));
  float3 const v_21 = powr(v_20, float3(3.0f));
  float3 const v_22 = v_21;
  float v_23 = 0.0f;
  if ((v_22.x > 0.00885645207017660141f)) {
    v_23 = v_22.x;
  } else {
    v_23 = (((116.0f * v_20.x) - 16.0f) * 0.00110705639235675335f);
  }
  float v_24 = 0.0f;
  if ((v_19.x > 8.00000095367431640625f)) {
    v_24 = v_22.y;
  } else {
    v_24 = (v_19.x * 0.00110705639235675335f);
  }
  float v_25 = 0.0f;
  if ((v_22.z > 0.00885645207017660141f)) {
    v_25 = v_22.z;
  } else {
    v_25 = (((116.0f * v_20.z) - 16.0f) * 0.00110705639235675335f);
  }
  float3 const v_26 = float3(v_23, v_24, v_25);
  return (v_26 * float3(0.96429562568664550781f, 1.0f, 0.82510453462600708008f));
}

float3 v_27(float3 v_28) {
  float const v_29 = ((v_28.x + (0.39633777737617492676f * v_28.y)) + (0.21580375730991363525f * v_28.z));
  float const v_30 = ((v_28.x - (0.10556134581565856934f * v_28.y)) - (0.06385417282581329346f * v_28.z));
  float const v_31 = ((v_28.x - (0.08948417752981185913f * v_28.y)) - (1.29148554801940917969f * v_28.z));
  float const v_32 = ((v_29 * v_29) * v_29);
  float const v_33 = ((v_30 * v_30) * v_30);
  float const v_34 = ((v_31 * v_31) * v_31);
  return float3((((4.07674169540405273438f * v_32) - (3.30771160125732421875f * v_33)) + (0.23096993565559387207f * v_34)), (((-1.26843798160552978516f * v_32) + (2.60975742340087890625f * v_33)) - (0.3413193821907043457f * v_34)), (((-0.00419608643278479576f * v_32) - (0.70341861248016357422f * v_33)) + (1.70761466026306152344f * v_34)));
}

float3 v_35(float3 v_36) {
  float3 v_37 = v_36;
  float2 const v_38 = v_37.yz;
  float v_39 = 0.0f;
  float2 v_40 = 0.0f;
  float2 v_41 = 0.0f;
  float const v_42 = dot(v_38, float2(0.40970200300216674805f, -0.91221898794174194336f));
  if ((v_42 < 0.0f)) {
    float const v_43 = dot(v_38, float2(0.46027600765228271484f, 0.88777601718902587891f));
    if ((v_43 < 0.0f)) {
      float const v_44 = dot(v_38, float2(-0.17112199962139129639f, 0.98524999618530273438f));
      if ((v_44 < 0.0f)) {
        v_39 = 0.1020469963550567627f;
        v_40 = float2(-0.01480400003492832184f, -0.16260799765586853027f);
        v_41 = float2(-0.27678599953651428223f, 0.00419300002977252007f);
      } else {
        v_39 = 0.09202899783849716187f;
        v_40 = float2(-0.03853299841284751892f, -0.00164999999105930328f);
        v_41 = float2(-0.23257200419902801514f, -0.09433099627494812012f);
      }
    } else {
      float const v_45 = dot(v_38, float2(0.94792497158050537109f, 0.31849500536918640137f));
      if ((v_45 < 0.0f)) {
        v_39 = 0.08170899748802185059f;
        v_40 = float2(-0.03460099920630455017f, -0.0022150001022964716f);
        v_41 = float2(0.01218499988317489624f, 0.33803099393844604492f);
      } else {
        v_39 = 0.09113200008869171143f;
        v_40 = float2(0.07037000358104705811f, 0.03413899987936019897f);
        v_41 = float2(0.01816999912261962891f, 0.37854999303817749023f);
      }
    }
  } else {
    float const v_46 = dot(v_38, float2(-0.9067999720573425293f, 0.42156198620796203613f));
    if ((v_46 < 0.0f)) {
      float const v_47 = dot(v_38, float2(-0.39791899919509887695f, -0.91742098331451416016f));
      if ((v_47 < 0.0f)) {
        v_39 = 0.11390200257301330566f;
        v_40 = float2(0.09083600342273712158f, 0.03625100106000900269f);
        v_41 = float2(0.22678099572658538818f, 0.01876400038599967957f);
      } else {
        v_39 = 0.1617390066385269165f;
        v_40 = float2(-0.00820199958980083466f, -0.26481899619102478027f);
        v_41 = float2(0.18715600669384002686f, -0.28430399298667907715f);
      }
    } else {
      v_39 = 0.1020469963550567627f;
      v_40 = float2(-0.01480400003492832184f, -0.16260799765586853027f);
      v_41 = float2(-0.27678599953651428223f, 0.00419300002977252007f);
    }
  }
  float v_48 = 1.0f;
  float const v_49 = dot(v_40, v_38);
  float const v_50 = v_49;
  if ((v_50 > 0.0f)) {
    float const v_51 = (1.0f - v_37.x);
    float const v_52 = (v_39 * v_51);
    if ((v_52 < v_50)) {
      float const v_53 = min(v_48, (v_52 / v_50));
      v_48 = v_53;
    }
  }
  float const v_54 = dot(v_41, v_38);
  float const v_55 = v_54;
  if ((v_55 > 0.0f)) {
    float const v_56 = v_37.x;
    float const v_57 = (v_39 * v_56);
    if ((v_57 < v_55)) {
      float const v_58 = min(v_48, (v_57 / v_55));
      v_48 = v_58;
    }
  }
  v_37 = float3(v_37.x, (v_37.yz * v_48));
  float3 const v_59 = v_27(v_37);
  return v_59;
}

float2 v_60(float v_61, float v_62, float v_63, float v_64, float2 v_65) {
  bool v_66 = false;
  if ((v_63 == 0.0f)) {
    v_66 = (v_64 == 1.0f);
  } else {
    v_66 = false;
  }
  if (v_66) {
    float const v_67 = length(v_65);
    float const v_68 = ((v_67 * v_62) - v_61);
    return float2(v_68, 1.0f);
  } else {
    float const v_69 = dot(v_65, v_65);
    float const v_70 = (v_69 - (v_61 * v_61));
    float const v_71 = (2.0f * ((v_62 * v_61) + v_65.x));
    float v_72 = 0.0f;
    if ((v_63 == 0.0f)) {
      v_72 = (v_70 / v_71);
    } else {
      float const v_73 = ((v_71 * v_71) - ((4.0f * v_63) * v_70));
      if ((v_73 < 0.0f)) {
        return float2(0.0f, -1.0f);
      }
      float const v_74 = sign((1.0f - v_62));
      float const v_75 = v_74;
      float const v_76 = sqrt(v_73);
      v_72 = (v_64 * (v_71 + (v_75 * v_76)));
    }
    float const v_77 = sign(((v_72 * v_62) + v_61));
    float const v_78 = v_77;
    return float2(v_72, v_78);
  }
  /* unreachable */
  return 0.0f;
}

float4 v_79(float4 v_80, int v_81, int v_82) {
  float4 v_83 = v_80;
  if (bool(v_82)) {
    switch(v_81) {
      case 2:
      case 3:
      case 4:
      {
        float const v_84 = max(v_83.w, 0.00009999999747378752f);
        v_83 = float4((v_83.xyz / v_84), v_83.w);
        break;
      }
      case 5:
      case 6:
      case 7:
      case 9:
      case 10:
      {
        float const v_85 = max(v_83.w, 0.00009999999747378752f);
        v_83 = float4(v_83.x, (v_83.yz / v_85), v_83.w);
        break;
      }
      default:
      {
        break;
      }
    }
  }
  switch(v_81) {
    case 2:
    {
      float3 const v_86 = v_18(v_83.xyz);
      v_83 = float4(v_86, v_83.w);
      break;
    }
    case 3:
    {
      float3 const v_87 = v_27(v_83.xyz);
      v_83 = float4(v_87, v_83.w);
      break;
    }
    case 4:
    {
      float3 const v_88 = v_35(v_83.xyz);
      v_83 = float4(v_88, v_83.w);
      break;
    }
    case 5:
    {
      float3 const v_89 = v(v_83.xyz);
      float3 const v_90 = v_18(v_89);
      v_83 = float4(v_90, v_83.w);
      break;
    }
    case 6:
    {
      float3 const v_91 = v(v_83.xyz);
      float3 const v_92 = v_27(v_91);
      v_83 = float4(v_92, v_83.w);
      break;
    }
    case 7:
    {
      float3 const v_93 = v(v_83.xyz);
      float3 const v_94 = v_35(v_93);
      v_83 = float4(v_94, v_83.w);
      break;
    }
    case 9:
    {
      float3 const v_95 = v_6(v_83.xyz);
      v_83 = float4(v_95, v_83.w);
      break;
    }
    case 10:
    {
      float3 v_96 = v_83.xyz;
      float3 v_97 = 0.0f;
      v_96 = float3(v_96.x, (v_96.yz * 0.00999999977648258209f));
      if (((v_96.y + v_96.z) >= 1.0f)) {
        v_97 = float3((v_96.y / (v_96.y + v_96.z)));
      } else {
        float3 const v_98 = v_6(float3(v_96.x, 100.0f, 50.0f));
        v_97 = v_98;
        v_97 = (v_97 * ((1.0f - v_96.y) - v_96.z));
        v_97 = (v_97 + v_96.y);
      }
      v_83 = float4(v_97, v_83.w);
      break;
    }
    default:
    {
      break;
    }
  }
  return v_83;
}

float2 v_99(int v_100, float2 v_101) {
  float2 v_102 = v_101;
  switch(v_100) {
    case 0:
    {
      float const v_103 = saturate(v_102.x);
      v_102.x = v_103;
      break;
    }
    case 1:
    {
      float const v_104 = fract(v_102.x);
      v_102.x = v_104;
      break;
    }
    case 2:
    {
      float const v_105 = (v_102.x - 1.0f);
      float const v_106 = floor((v_105 * 0.5f));
      v_102.x = ((v_105 - (2.0f * v_106)) - 1.0f);
      if (false) {
        float const v_107 = clamp(v_102.x, -1.0f, 1.0f);
        v_102.x = v_107;
      }
      float const v_108 = abs(v_102.x);
      v_102.x = v_108;
      break;
    }
    case 3:
    {
      bool v_109 = false;
      if ((v_102.x < 0.0f)) {
        v_109 = true;
      } else {
        v_109 = (v_102.x > 1.0f);
      }
      if (v_109) {
        return float2(0.0f, -1.0f);
      }
      break;
    }
    default:
    {
      break;
    }
  }
  return v_102;
}

float4 v_110(tint_array<float4, 4> v_111, float4 v_112, float2 v_113) {
  if ((v_113.y < 0.0f)) {
    return float4(0.0f);
  } else {
    if ((v_113.x <= v_112.x)) {
      return float4(v_111[0]);
    } else {
      if ((v_113.x < v_112.y)) {
        float4 const v_114 = mix(v_111[0], v_111[1], float4(((v_113.x - v_112.x) / (v_112.y - v_112.x))));
        return float4(v_114);
      } else {
        if ((v_113.x < v_112.z)) {
          float4 const v_115 = mix(v_111[1], v_111[2], float4(((v_113.x - v_112.y) / (v_112.z - v_112.y))));
          return float4(v_115);
        } else {
          if ((v_113.x < v_112.w)) {
            float4 const v_116 = mix(v_111[2], v_111[3], float4(((v_113.x - v_112.z) / (v_112.w - v_112.z))));
            return float4(v_116);
          } else {
            return float4(v_111[3]);
          }
        }
      }
    }
  }
  /* unreachable */
  return 0.0f;
}

float4 v_117(float4 v_118) {
  float const v_119 = max(v_118.w, 0.00009999999747378752f);
  return float4((v_118.xyz / v_119), v_118.w);
}

void v_120(tint_struct v_121, thread tint_struct_1* const v_122, tint_struct_2 v_123) {
  (*v_123.tint_member_23) = v_121.tint_member_1.y;
  float const v_124 = (*v_123.tint_member_7).tint_member_8[(*v_123.tint_member_23)].tint_member_12;
  float const v_125 = (*v_123.tint_member_7).tint_member_8[(*v_123.tint_member_23)].tint_member_13;
  float const v_126 = (*v_123.tint_member_7).tint_member_8[(*v_123.tint_member_23)].tint_member_14;
  float const v_127 = (*v_123.tint_member_7).tint_member_8[(*v_123.tint_member_23)].tint_member_15;
  float4x4 const v_128 = (*v_123.tint_member_7).tint_member_8[(*v_123.tint_member_23)].tint_member_9;
  float2 const v_129 = v_60(v_124, v_125, v_126, v_127, (v_128 * float4(v_121.tint_member_2, 0.0f, 1.0f)).xy);
  float2 v_130 = v_129;
  float2 const v_131 = v_99((*v_123.tint_member_7).tint_member_8[(*v_123.tint_member_23)].tint_member_16, v_130);
  v_130 = v_131;
  float4 const v_132 = v_110((*v_123.tint_member_7).tint_member_8[(*v_123.tint_member_23)].tint_member_10, (*v_123.tint_member_7).tint_member_8[(*v_123.tint_member_23)].tint_member_11, v_130);
  float4 const v_133 = v_132;
  float4 const v_134 = v_79(v_133, (*v_123.tint_member_7).tint_member_8[(*v_123.tint_member_23)].tint_member_17, (*v_123.tint_member_7).tint_member_8[(*v_123.tint_member_23)].tint_member_18);
  float4 v_135 = v_134;
  float2 const v_136 = (*v_123.tint_member_7).tint_member_8[(*v_123.tint_member_23)].tint_member_19;
  if ((v_136.x < 0.0f)) {
    float4 const v_137 = v_117(v_135);
    v_135 = v_137;
  } else {
    float const v_138 = v_136.x;
    float const v_139 = v_136.y;
    float const v_140 = max(v_135.w, v_138);
    v_135.w = v_140;
    float const v_141 = max(v_135.w, v_139);
    v_135 = float4((v_135.xyz * v_141), v_135.w);
  }
  float4 const v_142 = float4((*v_123.tint_member_7).tint_member_8[(*v_123.tint_member_23)].tint_member_20);
  float4 const v_143 = (*v_123.tint_member_7).tint_member_8[(*v_123.tint_member_23)].tint_member_21;
  float2 const v_144 = (v_143.xy + (v_143.zw * float2(v_135.w, v_142.w)));
  float4 const v_145 = ((v_142 * v_144.x) + (v_135 * v_144.y));
  float const v_146 = (v_123.tint_member_25.sample(v_123.tint_member_24, (v_121.tint_member.xy * 0.125f), bias(clamp(-0.47499999403953552246f, -16.0f, 15.9899997711181640625f))).x - 0.5f);
  float3 const v_147 = (v_145.xyz + (v_146 * (*v_123.tint_member_7).tint_member_8[(*v_123.tint_member_23)].tint_member_22));
  float3 const v_148 = clamp(v_147, float3(0.0f), float3(v_145.w));
  float4 const v_149 = float4(v_148, v_145.w);
  float4 v_150 = float4(1.0f);
  float2 const v_151 = clamp(v_121.tint_member_4, v_121.tint_member_3.xy, v_121.tint_member_3.zw);
  float const v_152 = v_123.tint_member_27.sample(v_123.tint_member_26, v_151, bias(clamp(-0.47499999403953552246f, -16.0f, 15.9899997711181640625f))).x;
  float const v_153 = mix(v_152, (1.0f - v_152), v_121.tint_member_5);
  v_150 = float4(v_153);
  (*v_122).tint_member_6 = (v_149 * v_150);
}

tint_struct_1 v_154(tint_struct v_155, tint_struct_2 v_156) {
  tint_struct_1 v_157 = {};
  v_120(v_155, (&v_157), v_156);
  return v_157;
}

fragment tint_struct_5 dawn_entry_point(float4 v_159 [[position]], tint_struct_6 v_160 [[stage_in]], const device tint_struct_3* v_161 [[buffer(2)]], sampler v_162 [[sampler(0)]], texture2d<float, access::sample> v_163 [[texture(0)]], sampler v_164 [[sampler(1)]], texture2d<float, access::sample> v_165 [[texture(1)]]) {
  thread uint v_166 = 0u;
  tint_struct_2 const v_167 = tint_struct_2{.tint_member_7=v_161, .tint_member_23=(&v_166), .tint_member_24=v_162, .tint_member_25=v_163, .tint_member_26=v_164, .tint_member_27=v_165};
  tint_struct_5 v_168 = {};
  v_168.tint_member_28 = v_154(tint_struct{.tint_member=v_159, .tint_member_1=v_160.tint_member_29, .tint_member_2=v_160.tint_member_30, .tint_member_3=v_160.tint_member_31, .tint_member_4=v_160.tint_member_32, .tint_member_5=v_160.tint_member_33}, v_167).tint_member_6;
  return v_168;
}
       dawn_entry_point                      