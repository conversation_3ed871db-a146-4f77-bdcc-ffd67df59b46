<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manus - AI Assistant</title>
    <style>
        /* Simulated Manus.im styles for testing */
        .flex { display: flex; }
        .flex-col { flex-direction: column; }
        .overflow-auto { overflow: auto; }
        .pt-2 { padding-top: 0.5rem; }
        .pb-5 { padding-bottom: 1.25rem; }
        .px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
        .group { /* group class */ }
        .h-14 { height: 3.5rem; }
        .truncate { overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
        .text-sm { font-size: 0.875rem; }
        .font-medium { font-weight: 500; }
        .text-xs { font-size: 0.75rem; }
        .whitespace-nowrap { white-space: nowrap; }
        .min-w-0 { min-width: 0; }
        .flex-1 { flex: 1; }
        .sticky { position: sticky; }
        .top-0 { top: 0; }
        .text-lg { font-size: 1.125rem; }
        .text-ellipsis { text-overflow: ellipsis; }
        .overflow-hidden { overflow: hidden; }
        .items-end { align-items: flex-end; }
        .p-3 { padding: 0.75rem; }
        .prose { /* prose styling */ }
        .float-right { float: right; }
        .w-full { width: 100%; }
        .h-full { height: 100%; }
        .object-cover { object-fit: cover; }
        .leading-5 { line-height: 1.25rem; }
        
        /* Custom variables simulation */
        .text-\[var\(--text-tertiary\)\] { color: #6b7280; }
        .text-\[var\(--text-primary\)\] { color: #111827; }
        .bg-\[var\(--fill-tsp-white-main\)\] { background-color: #ffffff; }
        .rounded-\[10px\] { border-radius: 10px; }
        .rounded-\[12px\] { border-radius: 12px; }
        .text-\[12px\] { font-size: 12px; }
        
        /* Layout */
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }
        .sidebar { width: 300px; background: #f8f9fa; padding: 1rem; }
        .main-content { flex: 1; padding: 1rem; }
        footer { background: #f1f3f4; padding: 1rem; }
    </style>
</head>
<body>
    <!-- Sidebar with task list -->
    <div class="sidebar">
        <div class="flex flex-col overflow-auto pt-2 pb-5">
            <div class="px-2">
                <!-- Task Item 1 -->
                <div class="group flex h-14" data-task-id="task1">
                    <div>
                        <img src="/icons/task-icon.png" alt="Task Icon" style="width: 24px; height: 24px;">
                    </div>
                    <div style="flex: 1; margin-left: 8px;">
                        <span class="truncate text-sm font-medium" title="Build a web scraper for Manus.im">
                            Build a web scraper for Manus.im
                        </span>
                        <div style="display: flex; justify-content: space-between;">
                            <span class="min-w-0 flex-1 truncate text-xs" title="Help me create a Python script that can...">
                                Help me create a Python script that can...
                            </span>
                            <span class="text-[var(--text-tertiary)] text-xs whitespace-nowrap">14:46</span>
                        </div>
                    </div>
                </div>
                
                <!-- Task Item 2 -->
                <div class="group flex h-14" data-task-id="task2">
                    <div>
                        <img src="/icons/task-icon.png" alt="Task Icon" style="width: 24px; height: 24px;">
                    </div>
                    <div style="flex: 1; margin-left: 8px;">
                        <span class="truncate text-sm font-medium" title="API Documentation Review">
                            API Documentation Review
                        </span>
                        <div style="display: flex; justify-content: space-between;">
                            <span class="min-w-0 flex-1 truncate text-xs" title="Please review the FastAPI documentation...">
                                Please review the FastAPI documentation...
                            </span>
                            <span class="text-[var(--text-tertiary)] text-xs whitespace-nowrap">13:22</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- New Task Button -->
        <button style="width: 100%; padding: 8px; background: #007bff; color: white; border: none; border-radius: 4px;">
            New task
        </button>
    </div>

    <!-- Main Content Area -->
    <div class="main-content">
        <!-- Current Task Title -->
        <div class="sticky top-0" style="background: white; padding: 1rem 0;">
            <div class="text-[var(--text-primary)] text-lg font-medium">
                <span class="whitespace-nowrap text-ellipsis overflow-hidden">
                    Build a web scraper for Manus.im
                </span>
            </div>
        </div>

        <!-- Chat Messages -->
        <div>
            <!-- User Message -->
            <div data-event-id="OZXIxaFvk9I5cn63HJKzZN" style="margin-bottom: 1rem;">
                <div class="items-end" style="display: flex; justify-content: flex-end;">
                    <div class="p-3 ltr:rounded-br-none" style="background: #007bff; color: white; border-radius: 12px; max-width: 70%;">
                        <span class="u-break-words">
                            I need help building a web scraper for Manus.im using Python and Playwright. 
                            Can you help me create a comprehensive solution with FastAPI backend?
                        </span>
                    </div>
                </div>
                <div class="float-right text-[12px]" style="margin-top: 4px; color: #6b7280;">
                    14:45
                </div>
            </div>

            <!-- Manus Response -->
            <div data-event-id="BXYJyaGwl0J6do74IKLaZO" style="margin-bottom: 1rem;">
                <div style="display: flex;">
                    <div style="flex: 1;">
                        <div class="prose" style="background: #f8f9fa; padding: 1rem; border-radius: 12px;">
                            <p>I'll help you build a comprehensive web scraper for Manus.im using Python, Playwright, and FastAPI. Here's a complete solution:</p>
                            
                            <h3>Project Structure</h3>
                            <p>We'll create a modular application with the following components:</p>
                            <ul>
                                <li>FastAPI backend with WebSocket support</li>
                                <li>Playwright for web scraping</li>
                                <li>Docker containerization</li>
                                <li>Realtime updates via WebSocket</li>
                            </ul>

                            <!-- File Attachment -->
                            <div class="rounded-[10px] bg-[var(--fill-tsp-white-main)] group/attach" style="margin: 1rem 0; padding: 1rem; border: 1px solid #e5e7eb;">
                                <div class="text-sm text-[var(--text-primary)]" style="font-weight: 500;">
                                    crawler.py
                                </div>
                                <div class="text-xs text-[var(--text-tertiary)]">
                                    Python • 15.2 KB
                                </div>
                            </div>

                            <p>This solution provides a robust foundation for scraping dynamic web content with proper error handling and realtime feedback.</p>
                        </div>
                    </div>
                </div>
                <div class="float-right text-[12px]" style="margin-top: 4px; color: #6b7280;">
                    14:46
                </div>
            </div>
        </div>

        <!-- Input Area -->
        <div style="position: fixed; bottom: 0; left: 300px; right: 0; background: white; padding: 1rem; border-top: 1px solid #e5e7eb;">
            <textarea placeholder="Send message to Manus" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; resize: none;" rows="3"></textarea>
        </div>
    </div>

    <!-- Footer -->
    <footer style="position: fixed; bottom: 0; left: 0; width: 300px; background: #f1f3f4; padding: 1rem;">
        <div style="display: flex; align-items: center;">
            <img class="w-full h-full object-cover" src="/avatars/user-avatar.png" alt="User Avatar" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px;">
            <span class="text-sm leading-5 font-medium text-[var(--text-primary)]">
                John Doe
            </span>
        </div>
    </footer>

    <!-- Computer Preview (if viewing file details) -->
    <div id="computer" style="display: none;">
        <div class="text-[var(--text-tertiary)]" style="padding: 1rem; border-bottom: 1px solid #e5e7eb;">
            crawler.py
        </div>
        <div class="monaco-editor">
            <div class="view-lines">
                <div>import asyncio</div>
                <div>from playwright.async_api import async_playwright</div>
                <div></div>
                <div>async def crawl_page():</div>
                <div>    async with async_playwright() as p:</div>
                <div>        browser = await p.chromium.launch()</div>
                <div>        # ... crawling logic</div>
            </div>
        </div>
    </div>
</body>
</html>
