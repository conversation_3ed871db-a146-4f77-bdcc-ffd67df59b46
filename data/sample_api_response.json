{"success": true, "data": {"page_title": "Test Page - <PERSON><PERSON>", "tasks": [], "current_task_title": "Test Task", "chat_messages": [{"event_id": "user-msg-123", "type": "user", "user_message": "Can you help me with Python code?", "timestamp": "2024-01-01 10:00"}, {"event_id": "manus-msg-456", "type": "manus", "manus_message": "I'll help you with Python programming. Here's a simple example: def hello_world(): print(\"Hello, World!\") return \"success\" This function demonstrates basic Python syntax.", "manus_html": "<div class=\"prose\"><p>I'll help you with Python programming. Here's a simple example:</p><pre><code class=\"language-python\">def hello_world():\n    print(\"Hello, World!\")\n    return \"success\"</code></pre><p>This function demonstrates basic Python syntax.</p></div>", "timestamp": "2024-01-01 10:01", "message_subtype": "code", "content_analysis": {"has_content": true, "text_length": 150, "html_length": 300, "has_code_blocks": true, "code_blocks_count": 1, "detected_languages": ["python"], "has_lists": false, "list_items_count": 0, "file_references": []}}], "footer_user": {}, "profile_status": {"profile_name": null, "use_system_profile": false, "headless": true}}, "message": "Crawl thành công"}