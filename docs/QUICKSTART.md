# 🚀 Manus Crawler - Quick Start Guide

Hướng dẫn nhanh để chạy ứng dụng Manus Crawler trong vài phút!

## 📋 Y<PERSON>u cầu hệ thống

- **Python 3.8+** 
- **pip** (Python package manager)
- **Git** (để clone repository)
- **Docker** (tùy chọn, cho containerized deployment)

## ⚡ Cài đặt nhanh

### Phương pháp 1: Sử dụng Script tự động (Khuyến nghị)

#### Trên Linux/macOS:
```bash
# Chạy script setup
./setup.sh
```

#### Trên Windows:
```cmd
# Chạy script setup
setup.bat
```

Script sẽ tự động:
- ✅ Kiểm tra Python và pip
- ✅ Tạo virtual environment (tùy chọn)
- ✅ Cài đặt dependencies
- ✅ Cài đặt Playwright browsers
- ✅ Chạy tests
- ✅ Khởi động server (tùy chọn)

### Phương pháp 2: <PERSON>ài đặt thủ công

```bash
# 1. Cài đặt dependencies
pip install -r requirements.txt

# 2. <PERSON><PERSON>i đặt Playwright browsers
playwright install chromium

# 3. Chạy tests (tùy chọn)
python test_app.py

# 4. Khởi động server
python main.py
```

### Phương pháp 3: Sử dụng Docker

```bash
# Build và chạy với Docker Compose
docker-compose up --build
```

## 🎯 Sử dụng ứng dụng

### 1. Truy cập giao diện web
Mở trình duyệt và truy cập: **http://localhost:8000/ui**

### 2. Crawl dữ liệu
1. **Nhập URL**: Mặc định là `https://manus.im/`
2. **Chọn Profile**: Tùy chọn tên Chrome profile để lưu session
3. **Chọn chế độ**: Headless (không hiển thị trình duyệt) hoặc non-headless
4. **Bấm "Bắt đầu Crawl"**: Theo dõi tiến trình realtime
5. **Xem kết quả**: Dữ liệu JSON sẽ hiển thị sau khi hoàn thành

### 3. API Documentation
Truy cập: **http://localhost:8000/docs** để xem API documentation đầy đủ

## 🔧 Tính năng chính

### ✨ Crawl Realtime
- Theo dõi tiến trình crawl realtime qua WebSocket
- Hiển thị log chi tiết từng bước
- Kết quả JSON được format đẹp

### 🎭 Chrome Profile Management
- Lưu session đăng nhập trong Chrome profile
- Sử dụng profile hệ thống hoặc tạo profile riêng
- Endpoint admin để setup profile thủ công

### 🐳 Docker Support
- Containerized deployment
- Volume persistence cho Chrome profiles
- Easy scaling và deployment

### 📡 API Endpoints
- `POST /crawl-url-realtime/` - Crawl URL với realtime updates
- `POST /crawl-html-realtime/` - Parse HTML content
- `POST /admin/setup-chrome-profile/` - Setup Chrome profile (cần API key)
- `GET /health` - Health check
- `WebSocket /ws/crawl-status/{request_id}` - Realtime updates

## 🛠️ Cấu hình

### Environment Variables (.env)
```env
ADMIN_API_KEY=your_super_secret_key_here
CHROME_PROFILE_BASE_PATH=./chrome_profiles
PYTHONUNBUFFERED=1
```

### Chrome Profiles
- **Profile tùy chỉnh**: Nhập tên profile để tạo/sử dụng
- **Profile hệ thống**: Check "Use system profile"
- **Profile tạm thời**: Để trống (mặc định)

## 🧪 Test ứng dụng

```bash
# Chạy test suite
python test_app.py
```

Test sẽ kiểm tra:
- ✅ Cấu trúc file
- ✅ Environment variables
- ✅ Selectors
- ✅ HTML parsing (nếu có dependencies)

## 📚 Ví dụ sử dụng

### Crawl URL với cURL
```bash
curl -X POST "http://localhost:8000/crawl-url-realtime/" \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://manus.im/",
    "request_id": "test-123",
    "headless": true,
    "profile_name": "my_profile"
  }'
```

### Setup Chrome Profile (Admin)
```bash
curl -X POST "http://localhost:8000/admin/setup-chrome-profile/" \
  -H "Content-Type: application/json" \
  -H "X-API-KEY: your_super_secret_key_here" \
  -d '{
    "profile_name": "manus_profile",
    "url": "https://manus.im/"
  }'
```

## 🚨 Troubleshooting

### Lỗi thường gặp:

1. **"playwright not found"**
   ```bash
   pip install playwright
   playwright install chromium
   ```

2. **"Permission denied"**
   ```bash
   chmod +x setup.sh
   ```

3. **"Port 8000 already in use"**
   - Thay đổi port trong `main.py`: `uvicorn.run(app, host="0.0.0.0", port=8001)`
   - Hoặc kill process đang dùng port 8000

4. **"Chrome profile error"**
   - Xóa thư mục `chrome_profiles` và tạo lại
   - Kiểm tra quyền truy cập thư mục

5. **"WebSocket connection failed"**
   - Kiểm tra firewall
   - Đảm bảo port 8000 không bị block

## 📞 Hỗ trợ

- 📖 **Tài liệu chi tiết**: Xem file `Required.md`
- 🐛 **Bug reports**: Tạo issue trên repository
- 💡 **Feature requests**: Tạo issue với label "enhancement"

## 🎉 Hoàn thành!

Bây giờ bạn đã có một ứng dụng crawl dữ liệu Manus.im hoàn chỉnh với:
- ✅ FastAPI backend
- ✅ Playwright crawler
- ✅ Realtime WebSocket updates
- ✅ Chrome profile management
- ✅ Docker support
- ✅ Beautiful frontend interface

**Happy Crawling! 🕷️**
