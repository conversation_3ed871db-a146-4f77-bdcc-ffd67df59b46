#!/usr/bin/env python3
"""
Script phân tích chi tiết các message từ file HTML thực của Manus
"""

import sys
import re
from pathlib import Path
from bs4 import BeautifulSoup

# Thêm thư mục root vào Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.core.message_classifier import ManusMessageClassifier


def extract_messages_from_html(html_file_path):
    """Trích xuất tất cả messages từ file HTML."""
    print(f"🔍 Đang phân tích file: {html_file_path}")

    with open(html_file_path, 'r', encoding='utf-8') as f:
        html_content = f.read()

    soup = BeautifulSoup(html_content, 'html.parser')

    # Tìm tất cả các message containers
    message_containers = soup.find_all('div', attrs={'data-event-id': True})

    print(f"📊 Tìm thấy {len(message_containers)} message containers")

    messages = []

    for i, container in enumerate(message_containers, 1):
        event_id = container.get('data-event-id')
        message_data = {
            'index': i,
            'event_id': event_id,
            'type': 'unknown',
            'content': '',
            'html': '',
            'structure_info': {}
        }

        # Phân tích cấu trúc container
        message_data['structure_info'] = analyze_container_structure(container)

        # Tìm user message
        user_msg = find_user_message(container)
        if user_msg:
            message_data['type'] = 'user'
            message_data['content'] = user_msg['text']
            message_data['html'] = user_msg['html']

        # Tìm manus message
        manus_msg = find_manus_message(container)
        if manus_msg:
            message_data['type'] = 'manus'
            message_data['content'] = manus_msg['text']
            message_data['html'] = manus_msg['html']

        # Tìm task/action message
        task_msg = find_task_message(container)
        if task_msg:
            message_data['type'] = 'task'
            message_data['content'] = task_msg['text']
            message_data['html'] = task_msg['html']

        messages.append(message_data)

    return messages


def analyze_container_structure(container):
    """Phân tích cấu trúc của message container."""
    info = {
        'classes': container.get('class', []),
        'has_prose': bool(container.find('div', class_='prose')),
        'has_user_message': bool(container.find('span', class_='u-break-words')),
        'has_task_header': bool(container.find('div', class_=lambda x: x and 'clickable' in x and 'group/header' in x)),
        'child_count': len(container.find_all()),
        'text_length': len(container.get_text().strip()),
    }

    # Tìm các elements đặc biệt
    special_elements = {
        'code_blocks': len(container.find_all(['code', 'pre'])),
        'lists': len(container.find_all(['ul', 'ol', 'li'])),
        'headings': len(container.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])),
        'links': len(container.find_all('a')),
        'images': len(container.find_all('img')),
        'buttons': len(container.find_all('button')),
        'svgs': len(container.find_all('svg')),
    }

    info['special_elements'] = special_elements
    return info


def find_user_message(container):
    """Tìm user message trong container."""
    # Pattern 1: span với class u-break-words trong div items-end
    user_span = container.find('div', class_=lambda x: x and 'items-end' in x)
    if user_span:
        text_span = user_span.find('span', class_='u-break-words')
        if text_span:
            return {
                'text': text_span.get_text().strip(),
                'html': str(text_span)
            }

    # Pattern 2: Tìm trực tiếp span với u-break-words
    text_span = container.find('span', class_='u-break-words')
    if text_span:
        # Kiểm tra xem có phải user message không (thường có background màu xanh)
        parent_div = text_span.find_parent('div')
        if parent_div and ('items-end' in str(parent_div.get('class', [])) or
                          'bg-[var(--fill-white)]' in str(parent_div.get('class', []))):
            return {
                'text': text_span.get_text().strip(),
                'html': str(text_span)
            }

    # Pattern 3: Kiểm tra container có class items-end (user message container)
    if 'items-end' in str(container.get('class', [])):
        all_text = container.get_text().strip()
        if all_text and len(all_text) > 10:  # Có nội dung đáng kể
            return {
                'text': all_text,
                'html': str(container)
            }

    return None


def find_manus_message(container):
    """Tìm Manus AI message trong container."""
    # Pattern 1: div với class prose
    prose_div = container.find('div', class_='prose')
    if prose_div:
        return {
            'text': prose_div.get_text().strip(),
            'html': str(prose_div)
        }

    # Pattern 2: div với class chứa prose
    prose_div = container.find('div', class_=lambda x: x and 'prose' in x)
    if prose_div:
        return {
            'text': prose_div.get_text().strip(),
            'html': str(prose_div)
        }

    return None


def find_task_message(container):
    """Tìm task/action message trong container."""
    # Tìm task header
    task_header = container.find('div', class_=lambda x: x and 'clickable' in x and 'group/header' in x)
    if task_header:
        title_div = task_header.find('div', class_='truncate font-medium')
        if title_div:
            return {
                'text': title_div.get_text().strip(),
                'html': str(task_header)
            }

    return None


def classify_and_analyze_message(message_data):
    """Phân loại và phân tích message."""
    if message_data['type'] == 'manus' and message_data['html']:
        # Sử dụng classifier để phân loại
        classification = ManusMessageClassifier.classify_message_type(
            manus_html=message_data['html'],
            manus_text=message_data['content']
        )

        message_data['classification'] = classification
        message_data['message_subtype'] = classification.get('message_subtype', 'unknown')
        message_data['confidence'] = classification.get('confidence', 0)
        message_data['content_analysis'] = classification.get('content_analysis', {})

    return message_data


def print_message_analysis(message_data):
    """In phân tích chi tiết của một message."""
    print(f"\n{'='*60}")
    print(f"📝 Message #{message_data['index']} - Event ID: {message_data['event_id']}")
    print(f"🏷️  Type: {message_data['type']}")

    if message_data['type'] == 'manus' and 'classification' in message_data:
        print(f"🎯 Subtype: {message_data['message_subtype']}")
        print(f"📊 Confidence: {message_data['confidence']:.2f}")

        analysis = message_data['content_analysis']
        if analysis:
            print(f"📈 Content Analysis:")
            print(f"   - Text length: {analysis.get('text_length', 0)} chars")
            print(f"   - HTML length: {analysis.get('html_length', 0)} chars")

            if analysis.get('has_code_blocks'):
                print(f"   - Code blocks: {analysis.get('code_blocks_count', 0)}")
                if analysis.get('detected_languages'):
                    print(f"   - Languages: {', '.join(analysis.get('detected_languages', []))}")

            if analysis.get('has_lists'):
                print(f"   - List items: {analysis.get('list_items_count', 0)}")

            if analysis.get('has_headings'):
                print(f"   - Headings: {analysis.get('headings_count', 0)}")

            if analysis.get('file_references'):
                print(f"   - File references: {len(analysis.get('file_references', []))}")

    # Structure info
    struct = message_data['structure_info']
    print(f"🏗️  Structure:")
    print(f"   - Child elements: {struct['child_count']}")
    print(f"   - Text length: {struct['text_length']} chars")
    print(f"   - Has prose: {struct['has_prose']}")
    print(f"   - Has user message: {struct['has_user_message']}")
    print(f"   - Has task header: {struct['has_task_header']}")

    special = struct['special_elements']
    if any(special.values()):
        print(f"   - Special elements: {dict(filter(lambda x: x[1] > 0, special.items()))}")

    # Content preview
    content_preview = message_data['content'][:200] + "..." if len(message_data['content']) > 200 else message_data['content']
    print(f"💬 Content Preview:")
    print(f"   {content_preview}")


def main():
    """Chạy phân tích chính."""
    html_file = Path(__file__).parent.parent / "data" / "html-manus.example.html"

    if not html_file.exists():
        print(f"❌ File không tồn tại: {html_file}")
        return

    print("🚀 Bắt đầu phân tích HTML Messages từ Manus")
    print("=" * 60)

    # Trích xuất messages
    messages = extract_messages_from_html(html_file)

    # Phân loại và phân tích từng message
    classified_messages = []
    for message in messages:
        classified_message = classify_and_analyze_message(message)
        classified_messages.append(classified_message)

    # Thống kê tổng quan
    print(f"\n📊 THỐNG KÊ TỔNG QUAN")
    print("=" * 60)

    type_counts = {}
    subtype_counts = {}

    for msg in classified_messages:
        msg_type = msg['type']
        type_counts[msg_type] = type_counts.get(msg_type, 0) + 1

        if msg_type == 'manus' and 'message_subtype' in msg:
            subtype = msg['message_subtype']
            subtype_counts[subtype] = subtype_counts.get(subtype, 0) + 1

    print(f"📈 Message Types:")
    for msg_type, count in type_counts.items():
        percentage = (count / len(classified_messages)) * 100
        print(f"   {msg_type}: {count} ({percentage:.1f}%)")

    if subtype_counts:
        print(f"\n🏷️  Manus Message Subtypes:")
        total_manus = type_counts.get('manus', 0)
        for subtype, count in subtype_counts.items():
            percentage = (count / total_manus) * 100 if total_manus > 0 else 0
            print(f"   {subtype}: {count} ({percentage:.1f}%)")

    # In chi tiết từng message
    print(f"\n📝 CHI TIẾT TỪNG MESSAGE")
    for message in classified_messages:
        print_message_analysis(message)

    print(f"\n🎉 Hoàn thành phân tích {len(classified_messages)} messages!")


if __name__ == "__main__":
    main()
