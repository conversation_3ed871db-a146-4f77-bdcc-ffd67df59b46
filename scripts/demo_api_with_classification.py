#!/usr/bin/env python3
"""
Demo script để test API với message classification
"""

import asyncio
import json
import sys
from pathlib import Path

# Thêm thư mục root vào Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.core.crawler import crawl_manus_page_content


async def demo_api_response():
    """Demo API response với message classification."""
    print("🚀 Demo API Response với Message Classification")
    print("=" * 60)
    
    # Test HTML với nhiều loại message khác nhau
    test_html = """
    <html>
        <head><title>Manus AI Chat - Demo</title></head>
        <body>
            <!-- User message 1 -->
            <div data-event-id="user-001">
                <div class="flex w-full flex-col items-end justify-end gap-1 group mt-3">
                    <span class="text-[var(--text-primary)] u-break-words whitespace-pre-wrap">
                        Hello! Can you help me create a Python web scraper?
                    </span>
                </div>
            </div>
            
            <!-- Manus response 1 - Mixed content -->
            <div data-event-id="manus-001">
                <div class="flex flex-col gap-2 w-full group mt-3">
                    <div class="prose prose-sm sm:prose-base dark:prose-invert">
                        <h2>Creating a Python Web Scraper</h2>
                        <p>I'll help you create a comprehensive web scraper. Here's what we'll cover:</p>
                        
                        <h3>Step 1: Install Dependencies</h3>
                        <pre><code class="language-bash">pip install requests beautifulsoup4 selenium</code></pre>
                        
                        <h3>Step 2: Basic Scraper Code</h3>
                        <pre><code class="language-python">
import requests
from bs4 import BeautifulSoup

def scrape_website(url):
    response = requests.get(url)
    soup = BeautifulSoup(response.content, 'html.parser')
    return soup.find_all('div', class_='content')
                        </code></pre>
                        
                        <h3>Files you'll need:</h3>
                        <ul>
                            <li>scraper.py - Main scraper logic</li>
                            <li>requirements.txt - Dependencies</li>
                            <li>config.py - Configuration settings</li>
                            <li>utils.py - Helper functions</li>
                        </ul>
                        
                        <p>This creates a solid foundation for web scraping projects.</p>
                    </div>
                </div>
            </div>
            
            <!-- User message 2 -->
            <div data-event-id="user-002">
                <div class="flex w-full flex-col items-end justify-end gap-1 group mt-3">
                    <span class="text-[var(--text-primary)] u-break-words whitespace-pre-wrap">
                        Can you show me just the basic code structure?
                    </span>
                </div>
            </div>
            
            <!-- Manus response 2 - Code content -->
            <div data-event-id="manus-002">
                <div class="flex flex-col gap-2 w-full group mt-3">
                    <div class="prose prose-sm sm:prose-base dark:prose-invert">
                        <p>Here's the basic code structure:</p>
                        <pre><code class="language-python">
class WebScraper:
    def __init__(self, base_url):
        self.base_url = base_url
        self.session = requests.Session()
    
    def scrape_page(self, endpoint):
        url = f"{self.base_url}/{endpoint}"
        response = self.session.get(url)
        return BeautifulSoup(response.content, 'html.parser')
    
    def extract_data(self, soup):
        # Extract specific data from the soup
        return soup.find_all('div', class_='data-item')
                        </code></pre>
                    </div>
                </div>
            </div>
            
            <!-- User message 3 -->
            <div data-event-id="user-003">
                <div class="flex w-full flex-col items-end justify-end gap-1 group mt-3">
                    <span class="text-[var(--text-primary)] u-break-words whitespace-pre-wrap">
                        What are the best practices for web scraping?
                    </span>
                </div>
            </div>
            
            <!-- Manus response 3 - List content -->
            <div data-event-id="manus-003">
                <div class="flex flex-col gap-2 w-full group mt-3">
                    <div class="prose prose-sm sm:prose-base dark:prose-invert">
                        <h3>Web Scraping Best Practices</h3>
                        <ol>
                            <li>Always check robots.txt before scraping</li>
                            <li>Implement rate limiting to avoid overwhelming servers</li>
                            <li>Use proper headers to identify your scraper</li>
                            <li>Handle errors gracefully with try-catch blocks</li>
                            <li>Respect website terms of service</li>
                            <li>Use caching to avoid redundant requests</li>
                            <li>Implement retry logic for failed requests</li>
                            <li>Monitor your scraper's performance</li>
                        </ol>
                        <p>Following these practices ensures ethical and efficient scraping.</p>
                    </div>
                </div>
            </div>
            
            <!-- User message 4 -->
            <div data-event-id="user-004">
                <div class="flex w-full flex-col items-end justify-end gap-1 group mt-3">
                    <span class="text-[var(--text-primary)] u-break-words whitespace-pre-wrap">
                        Thank you for the comprehensive guide!
                    </span>
                </div>
            </div>
            
            <!-- Manus response 4 - Text content -->
            <div data-event-id="manus-004">
                <div class="flex flex-col gap-2 w-full group mt-3">
                    <div class="prose prose-sm sm:prose-base dark:prose-invert">
                        <p>You're welcome! I'm glad I could help you understand web scraping fundamentals. 
                        Remember to always scrape responsibly and respect website policies. If you need 
                        help implementing any specific features or run into issues, feel free to ask!</p>
                    </div>
                </div>
            </div>
        </body>
    </html>
    """
    
    print(f"📄 Testing with comprehensive HTML content")
    print(f"📊 HTML size: {len(test_html):,} characters")
    
    try:
        # Gọi crawler function
        result = await crawl_manus_page_content(
            html_content=test_html,
            headless=True
        )
        
        if not result["success"]:
            print(f"❌ API call failed: {result.get('message', 'Unknown error')}")
            return False
        
        print(f"✅ API call successful!")
        
        # Phân tích response
        data = result.get("data", {})
        chat_messages = data.get("chat_messages", [])
        
        print(f"\n📊 API Response Analysis:")
        print(f"   - Success: {result['success']}")
        print(f"   - Page title: {data.get('page_title', 'N/A')}")
        print(f"   - Total messages: {len(chat_messages)}")
        
        # Phân tích từng message
        user_count = 0
        manus_count = 0
        classification_stats = {}
        
        print(f"\n💬 Message Details:")
        
        for i, msg in enumerate(chat_messages, 1):
            msg_type = msg.get("type", "unknown")
            event_id = msg.get("event_id", "N/A")
            
            print(f"\n   Message {i} (ID: {event_id}):")
            print(f"     Type: {msg_type}")
            
            if msg_type == "user":
                user_count += 1
                content = msg.get("user_message", "")
                print(f"     Content: {content[:80]}{'...' if len(content) > 80 else ''}")
                
            elif msg_type == "manus":
                manus_count += 1
                content = msg.get("manus_message", "")
                subtype = msg.get("message_subtype", "unclassified")
                content_analysis = msg.get("content_analysis", {})
                
                print(f"     Content: {content[:80]}{'...' if len(content) > 80 else ''}")
                print(f"     🏷️  Subtype: {subtype}")
                
                # Count subtypes
                classification_stats[subtype] = classification_stats.get(subtype, 0) + 1
                
                if content_analysis:
                    print(f"     📈 Analysis:")
                    print(f"        - Text length: {content_analysis.get('text_length', 'N/A')}")
                    print(f"        - Code blocks: {content_analysis.get('code_blocks_count', 0)}")
                    print(f"        - List items: {content_analysis.get('list_items_count', 0)}")
                    print(f"        - Headings: {content_analysis.get('headings_count', 0)}")
                    
                    languages = content_analysis.get('detected_languages', [])
                    if languages:
                        print(f"        - Languages: {', '.join(languages)}")
                    
                    file_refs = content_analysis.get('file_references', [])
                    if file_refs:
                        print(f"        - File refs: {', '.join(file_refs[:3])}{'...' if len(file_refs) > 3 else ''}")
        
        # Thống kê tổng quan
        print(f"\n📈 Statistics:")
        print(f"   👤 User messages: {user_count}")
        print(f"   🤖 Manus messages: {manus_count}")
        
        if classification_stats:
            print(f"   🏷️  Classification breakdown:")
            for subtype, count in classification_stats.items():
                percentage = (count / manus_count) * 100 if manus_count > 0 else 0
                print(f"      - {subtype}: {count} ({percentage:.1f}%)")
        
        # Test JSON serialization
        print(f"\n🔧 JSON Serialization Test:")
        try:
            json_str = json.dumps(result, indent=2, ensure_ascii=False)
            print(f"   ✅ Success - {len(json_str):,} characters")
            
            # Lưu response để inspection
            output_file = Path(__file__).parent.parent / "data" / "demo_api_response.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(json_str)
            print(f"   📁 Saved to: {output_file}")
            
        except Exception as json_error:
            print(f"   ❌ JSON serialization failed: {str(json_error)}")
            return False
        
        # Kiểm tra tất cả Manus messages có classification không
        classified_manus = sum(1 for msg in chat_messages 
                              if msg.get("type") == "manus" and msg.get("message_subtype"))
        
        print(f"\n🎯 Classification Coverage:")
        print(f"   Manus messages with classification: {classified_manus}/{manus_count}")
        if manus_count > 0:
            coverage = (classified_manus / manus_count) * 100
            print(f"   Coverage rate: {coverage:.1f}%")
            
            if coverage == 100:
                print(f"   🎉 Perfect classification coverage!")
                return True
            elif coverage >= 80:
                print(f"   ✅ Good classification coverage!")
                return True
            else:
                print(f"   ⚠️  Low classification coverage!")
                return False
        else:
            print(f"   ⚠️  No Manus messages found!")
            return False
            
    except Exception as e:
        print(f"❌ Demo failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main demo function."""
    print("🎯 Starting API Demo with Message Classification")
    
    success = await demo_api_response()
    
    if success:
        print(f"\n🎉 Demo completed successfully!")
        print(f"\n💡 Key Findings:")
        print(f"   • API response includes message_subtype for all Manus messages")
        print(f"   • Content analysis provides detailed breakdown")
        print(f"   • JSON serialization works perfectly")
        print(f"   • Classification covers: text, code, list, mixed content types")
        
        print(f"\n📋 How to use in your application:")
        print(f"   1. Call POST /crawl-html/ with your HTML content")
        print(f"   2. Check response.data.chat_messages[].message_subtype")
        print(f"   3. Use response.data.chat_messages[].content_analysis for details")
        print(f"   4. Filter/sort messages by type for better organization")
        
    else:
        print(f"\n⚠️  Demo had issues. Please check the implementation.")
    
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
