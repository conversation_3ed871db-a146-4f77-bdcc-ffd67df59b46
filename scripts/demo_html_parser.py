#!/usr/bin/env python3
"""
Demo HTML Parser - Test parsing HTML mà không cần Playwright
Sử dụng BeautifulSoup để parse HTML tĩnh
"""

import os
import json
from typing import Dict, Any, List

def parse_html_with_beautifulsoup(html_content: str) -> Dict[str, Any]:
    """
    Parse HTML content sử dụng BeautifulSoup thay vì Playwright.
    Đây là demo để test selectors mà không cần cài đặt Playwright.
    """
    try:
        from bs4 import BeautifulSoup
    except ImportError:
        print("❌ BeautifulSoup not installed. Install with: pip install beautifulsoup4")
        return {"success": False, "error": "BeautifulSoup not installed"}
    
    import manus_selectors as sel
    
    soup = BeautifulSoup(html_content, 'html.parser')
    
    data = {
        "page_title": "",
        "tasks": [],
        "current_task_title": "",
        "chat_messages": [],
        "footer_user": {},
        "profile_status": {
            "profile_name": None,
            "use_system_profile": False,
            "headless": True
        }
    }
    
    # Lấy title trang
    try:
        title_tag = soup.find('title')
        if title_tag:
            data["page_title"] = title_tag.get_text().strip()
    except:
        pass
    
    # Lấy danh sách tasks từ sidebar
    try:
        # Chuyển đổi CSS selector phức tạp thành BeautifulSoup selector
        # div.flex.flex-col.overflow-auto.pt-2.pb-5 > div.px-2 > div.group.flex.h-14
        task_containers = soup.select('div.group.flex.h-14')
        
        for container in task_containers:
            task_data = {}
            
            # Icon
            try:
                icon_img = container.find('img')
                if icon_img:
                    task_data["icon_src"] = icon_img.get('src')
            except:
                pass
            
            # Title
            try:
                title_span = container.find('span', class_='truncate text-sm font-medium'.split())
                if title_span and title_span.get('title'):
                    task_data["title"] = title_span.get('title')
                    task_data["title_text"] = title_span.get_text().strip()
            except:
                pass
            
            # Timestamp - tìm span có class chứa text-xs và whitespace-nowrap
            try:
                timestamp_span = container.find('span', class_='text-xs whitespace-nowrap'.split())
                if timestamp_span:
                    task_data["timestamp"] = timestamp_span.get_text().strip()
            except:
                pass
            
            # Preview
            try:
                preview_span = container.find('span', class_='min-w-0 flex-1 truncate text-xs'.split())
                if preview_span and preview_span.get('title'):
                    task_data["preview"] = preview_span.get('title')
            except:
                pass
            
            if task_data:
                data["tasks"].append(task_data)
                
    except Exception as e:
        print(f"⚠️  Lỗi khi parse tasks: {str(e)}")
    
    # Lấy tiêu đề task hiện tại
    try:
        # Tìm trong div.sticky.top-0
        current_title_elem = soup.select_one('div.sticky.top-0 span.whitespace-nowrap.text-ellipsis.overflow-hidden')
        if current_title_elem:
            data["current_task_title"] = current_title_elem.get_text().strip()
    except:
        pass
    
    # Lấy tin nhắn chat
    try:
        chat_events = soup.find_all('div', attrs={'data-event-id': True})
        
        for event in chat_events:
            message_data = {
                "event_id": event.get('data-event-id')
            }
            
            # Tin nhắn user - tìm trong div.items-end
            try:
                user_msg_container = event.find('div', class_='items-end')
                if user_msg_container:
                    user_msg_span = user_msg_container.find('span', class_='u-break-words')
                    if user_msg_span:
                        message_data["user_message"] = user_msg_span.get_text().strip()
                        message_data["type"] = "user"
            except:
                pass
            
            # Tin nhắn Manus - tìm div.prose
            try:
                manus_msg = event.find('div', class_='prose')
                if manus_msg:
                    message_data["manus_message"] = manus_msg.get_text().strip()
                    message_data["manus_html"] = str(manus_msg)
                    message_data["type"] = "manus"
            except:
                pass
            
            # Timestamp - tìm div có class float-right và text-[12px]
            try:
                timestamp_elem = event.find('div', class_='float-right')
                if timestamp_elem:
                    message_data["timestamp"] = timestamp_elem.get_text().strip()
            except:
                pass
            
            if "user_message" in message_data or "manus_message" in message_data:
                data["chat_messages"].append(message_data)
                
    except Exception as e:
        print(f"⚠️  Lỗi khi parse chat messages: {str(e)}")
    
    # Lấy thông tin user ở footer
    try:
        footer = soup.find('footer')
        if footer:
            footer_avatar = footer.find('img', class_='w-full h-full object-cover'.split())
            footer_name = footer.find('span', class_='text-sm leading-5 font-medium'.split())
            
            data["footer_user"] = {
                "avatar_src": footer_avatar.get('src') if footer_avatar else None,
                "name": footer_name.get_text().strip() if footer_name else None
            }
    except:
        pass
    
    return {
        "success": True,
        "data": data,
        "message": "HTML parsing completed with BeautifulSoup"
    }

def main():
    """Demo chính."""
    print("🧪 Demo HTML Parser - BeautifulSoup")
    print("=" * 50)
    
    # Đọc file HTML mẫu
    html_file = "html-manus.example.html"
    if not os.path.exists(html_file):
        print(f"❌ File {html_file} không tồn tại!")
        return
    
    print(f"📄 Đọc file: {html_file}")
    with open(html_file, 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    print(f"📊 File size: {len(html_content)} characters")
    
    # Parse HTML
    print("🔍 Parsing HTML content...")
    result = parse_html_with_beautifulsoup(html_content)
    
    if result["success"]:
        print("✅ Parsing thành công!")
        data = result["data"]
        
        print(f"\n📋 Kết quả:")
        print(f"   📄 Page title: {data.get('page_title', 'N/A')}")
        print(f"   📝 Tasks found: {len(data.get('tasks', []))}")
        print(f"   💬 Chat messages: {len(data.get('chat_messages', []))}")
        print(f"   🎯 Current task: {data.get('current_task_title', 'N/A')}")
        print(f"   👤 Footer user: {data.get('footer_user', {}).get('name', 'N/A')}")
        
        # In chi tiết tasks
        tasks = data.get('tasks', [])
        if tasks:
            print(f"\n📝 Tasks chi tiết:")
            for i, task in enumerate(tasks, 1):
                print(f"   Task {i}:")
                for key, value in task.items():
                    print(f"     {key}: {value}")
                print()
        
        # In chi tiết chat messages
        messages = data.get('chat_messages', [])
        if messages:
            print(f"💬 Chat messages chi tiết:")
            for i, msg in enumerate(messages, 1):
                print(f"   Message {i}:")
                for key, value in msg.items():
                    if key == 'manus_html':
                        print(f"     {key}: [HTML content - {len(str(value))} chars]")
                    else:
                        print(f"     {key}: {value}")
                print()
        
        # Lưu kết quả ra file JSON
        output_file = "demo_result.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"💾 Kết quả đã được lưu vào: {output_file}")
        
    else:
        print(f"❌ Parsing thất bại: {result.get('message', 'Unknown error')}")

if __name__ == "__main__":
    main()
