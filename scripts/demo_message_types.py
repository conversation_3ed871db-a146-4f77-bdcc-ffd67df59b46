#!/usr/bin/env python3
"""
Demo script để hiển thị các loại message classification
"""

import sys
import asyncio
from pathlib import Path

# Thêm thư mục root vào Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.core.message_classifier import ManusMessageClassifier


def create_sample_messages():
    """Tạo các message mẫu để demo."""
    return [
        {
            "name": "📝 Text Message",
            "description": "Phản hồi văn bản thông thường",
            "html": """
            <div class="prose">
                <p>Tôi hiểu yêu cầu của bạn. Manus.im là một nền tảng AI assistant rất mạnh mẽ 
                và việc tạo web scraper cho nó sẽ giúp bạn tự động hóa nhiều tác vụ.</p>
                <p>Chúng ta sẽ bắt đầu với việc phân tích cấu trúc trang web và xác định 
                các CSS selectors cần thiết.</p>
            </div>
            """,
            "text": "Tôi hiểu yêu cầu của bạn. Manus.im là một nền tảng AI assistant rất mạnh mẽ và việc tạo web scraper cho nó sẽ giúp bạn tự động hóa nhiều tác vụ. Chúng ta sẽ bắt đầu với việc phân tích cấu trúc trang web và xác định các CSS selectors cần thiết."
        },
        {
            "name": "💻 Code Message",
            "description": "Message chứa code snippets",
            "html": """
            <div class="prose">
                <p>Đây là code Python để khởi tạo web scraper:</p>
                <pre><code class="language-python">
import asyncio
from playwright.async_api import async_playwright

class ManusWebScraper:
    def __init__(self, headless=True):
        self.headless = headless
        self.base_url = "https://manus.im"
    
    async def start_browser(self):
        self.playwright = await async_playwright().start()
        self.browser = await self.playwright.chromium.launch(headless=self.headless)
        self.context = await self.browser.new_context()
        self.page = await self.context.new_page()
        
    async def crawl_page(self, url):
        await self.page.goto(url)
        return await self.page.content()
                </code></pre>
                <p>Code này sẽ tạo một class cơ bản để crawl trang web.</p>
            </div>
            """,
            "text": "Đây là code Python để khởi tạo web scraper: [CODE BLOCK] Code này sẽ tạo một class cơ bản để crawl trang web."
        },
        {
            "name": "📋 List Message", 
            "description": "Message chứa danh sách hoặc các bước",
            "html": """
            <div class="prose">
                <h3>Các bước để tạo web scraper hoàn chỉnh:</h3>
                <ol>
                    <li>Phân tích cấu trúc HTML của trang Manus.im</li>
                    <li>Xác định các CSS selectors cho từng element</li>
                    <li>Tạo file selectors.py để quản lý selectors</li>
                    <li>Viết logic crawl trong crawler.py</li>
                    <li>Tạo FastAPI server để expose API</li>
                    <li>Thêm WebSocket support cho realtime updates</li>
                    <li>Containerize với Docker</li>
                    <li>Test và debug</li>
                </ol>
                <p>Bước đầu tiên là quan trọng nhất vì nó quyết định độ chính xác của scraper.</p>
            </div>
            """,
            "text": "Các bước để tạo web scraper hoàn chỉnh: 1. Phân tích cấu trúc HTML của trang Manus.im 2. Xác định các CSS selectors cho từng element..."
        },
        {
            "name": "📁 File Message",
            "description": "Message chứa nhiều tham chiếu file",
            "html": """
            <div class="prose">
                <p>Bạn cần tạo cấu trúc project như sau:</p>
                <p>File crawler.py sẽ chứa logic crawl chính. File selectors.py sẽ định nghĩa CSS selectors. 
                File main.py sẽ là FastAPI server. File requirements.txt sẽ list dependencies.</p>
                <p>Ngoài ra còn có docker-compose.yml để containerize, package.json cho frontend, 
                config.yaml cho settings, và .env cho environment variables.</p>
                <p>File README.md sẽ chứa hướng dẫn sử dụng và setup.sh để tự động cài đặt.</p>
            </div>
            """,
            "text": "Bạn cần tạo cấu trúc project như sau: File crawler.py sẽ chứa logic crawl chính. File selectors.py sẽ định nghĩa CSS selectors..."
        },
        {
            "name": "🔀 Mixed Message",
            "description": "Message kết hợp nhiều loại nội dung",
            "html": """
            <div class="prose">
                <h2>Hướng dẫn tạo Web Scraper cho Manus.im</h2>
                <p>Tôi sẽ hướng dẫn bạn tạo một web scraper hoàn chỉnh với các tính năng sau:</p>
                
                <h3>1. Cài đặt Dependencies</h3>
                <pre><code class="language-bash">pip install playwright fastapi uvicorn beautifulsoup4</code></pre>
                
                <h3>2. Tạo Crawler Class</h3>
                <pre><code class="language-python">
class ManusWebScraper:
    def __init__(self):
        self.selectors = {
            'chat_messages': 'div[data-event-id]',
            'user_message': 'div.items-end span.u-break-words',
            'manus_message': 'div.prose'
        }
                </code></pre>
                
                <h3>3. Files cần tạo:</h3>
                <ul>
                    <li>crawler.py - Logic crawl chính</li>
                    <li>selectors.py - CSS selectors</li>
                    <li>main.py - FastAPI server</li>
                    <li>requirements.txt - Dependencies</li>
                    <li>docker-compose.yml - Container setup</li>
                </ul>
                
                <h3>4. Chạy ứng dụng</h3>
                <pre><code class="language-bash">uvicorn main:app --reload --host 0.0.0.0 --port 8000</code></pre>
                
                <p>Đây là một dự án phức tạp với nhiều thành phần khác nhau. Bạn cần hiểu rõ từng bước để implement thành công.</p>
            </div>
            """,
            "text": "Hướng dẫn tạo Web Scraper cho Manus.im. Tôi sẽ hướng dẫn bạn tạo một web scraper hoàn chỉnh với các tính năng sau..."
        }
    ]


def display_classification_result(name, description, result):
    """Hiển thị kết quả phân loại một cách đẹp mắt."""
    print(f"\n{name}")
    print(f"📖 {description}")
    print(f"🏷️  Classified as: {result['message_subtype']}")
    print(f"🎯 Confidence: {result['confidence']:.1f}")
    print(f"💭 Reasoning: {result['reasoning']}")
    
    analysis = result.get('content_analysis', {})
    if analysis:
        print(f"📊 Content Analysis:")
        
        if analysis.get('has_code_blocks'):
            print(f"   💻 Code blocks: {analysis.get('code_blocks_count', 0)}")
            if analysis.get('detected_languages'):
                print(f"   🔤 Languages: {', '.join(analysis.get('detected_languages', []))}")
        
        if analysis.get('has_lists'):
            print(f"   📋 List items: {analysis.get('list_items_count', 0)}")
        
        if analysis.get('has_headings'):
            print(f"   📑 Headings: {analysis.get('headings_count', 0)}")
        
        if analysis.get('file_references'):
            print(f"   📁 File references: {len(analysis.get('file_references', []))}")
            if len(analysis.get('file_references', [])) <= 5:
                print(f"      Files: {', '.join(analysis.get('file_references', []))}")
        
        print(f"   📏 Text length: {analysis.get('text_length', 0)} characters")


def main():
    """Demo chính."""
    print("🚀 Demo Message Classification System")
    print("=" * 60)
    print("Hệ thống phân loại message của Manus AI có thể nhận diện các loại:")
    print("📝 Text - Phản hồi văn bản thông thường")
    print("💻 Code - Chứa code snippets và programming content")
    print("📋 List - Chứa danh sách, bước hướng dẫn")
    print("📁 File - Nhiều tham chiếu đến files")
    print("🔀 Mixed - Kết hợp nhiều loại nội dung")
    print("=" * 60)
    
    # Tạo sample messages
    sample_messages = create_sample_messages()
    
    # Phân loại từng message
    for message in sample_messages:
        try:
            result = ManusMessageClassifier.classify_message_type(
                manus_html=message['html'],
                manus_text=message['text']
            )
            
            display_classification_result(
                message['name'],
                message['description'], 
                result
            )
            
        except Exception as e:
            print(f"\n❌ Error classifying {message['name']}: {str(e)}")
    
    print(f"\n" + "=" * 60)
    print("🎉 Demo completed! Message classification system is working.")
    print("💡 Tip: Bạn có thể sử dụng hệ thống này để phân tích và tổ chức")
    print("   các phản hồi từ Manus AI một cách tự động.")


if __name__ == "__main__":
    main()
