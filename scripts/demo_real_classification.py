#!/usr/bin/env python3
"""
Demo script showcasing message classification với dữ liệu thực từ Manus
"""

import sys
import asyncio
from pathlib import Path

# Thêm thư mục root vào Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.core.crawler import crawl_manus_page_content


async def demo_real_classification():
    """Demo phân loại message với dữ liệu HTML thực."""
    print("🚀 Demo Message Classification với dữ liệu thực từ Manus")
    print("=" * 70)
    
    # Đọc file HTML thực
    html_file = Path(__file__).parent.parent / "data" / "html-manus.example.html"
    
    if not html_file.exists():
        print(f"❌ File không tồn tại: {html_file}")
        return
    
    with open(html_file, 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    print(f"📄 Đang phân tích file: {html_file.name}")
    print(f"📊 File size: {len(html_content):,} characters")
    
    # Crawl và phân loại messages
    try:
        result = await crawl_manus_page_content(
            html_content=html_content,
            headless=True
        )
        
        if not result["success"]:
            print(f"❌ Crawl thất bại: {result.get('message', 'Unknown error')}")
            return
        
        data = result["data"]
        chat_messages = data.get('chat_messages', [])
        
        print(f"\n✅ Crawl thành công!")
        print(f"📊 Tổng quan:")
        print(f"   - Page title: {data.get('page_title', 'N/A')}")
        print(f"   - Tasks found: {len(data.get('tasks', []))}")
        print(f"   - Chat messages: {len(chat_messages)}")
        
        # Phân tích thống kê
        stats = analyze_message_stats(chat_messages)
        display_statistics(stats)
        
        # Hiển thị examples cho từng loại
        display_examples_by_type(chat_messages)
        
    except Exception as e:
        print(f"❌ Lỗi: {str(e)}")
        import traceback
        traceback.print_exc()


def analyze_message_stats(messages):
    """Phân tích thống kê messages."""
    stats = {
        'total': len(messages),
        'by_type': {},
        'by_subtype': {},
        'content_analysis': {
            'avg_length': 0,
            'total_code_blocks': 0,
            'total_list_items': 0,
            'languages_detected': set(),
            'file_references': set()
        }
    }
    
    total_length = 0
    
    for msg in messages:
        # Count by type
        msg_type = msg.get('type', 'unknown')
        stats['by_type'][msg_type] = stats['by_type'].get(msg_type, 0) + 1
        
        # Count by subtype (for manus messages)
        if msg_type == 'manus':
            subtype = msg.get('message_subtype', 'unknown')
            stats['by_subtype'][subtype] = stats['by_subtype'].get(subtype, 0) + 1
            
            # Content analysis
            content_analysis = msg.get('content_analysis', {})
            if content_analysis:
                stats['content_analysis']['total_code_blocks'] += content_analysis.get('code_blocks_count', 0)
                stats['content_analysis']['total_list_items'] += content_analysis.get('list_items_count', 0)
                
                # Languages
                languages = content_analysis.get('detected_languages', [])
                stats['content_analysis']['languages_detected'].update(languages)
                
                # File references
                file_refs = content_analysis.get('file_references', [])
                stats['content_analysis']['file_references'].update(file_refs)
        
        # Text length
        text = msg.get('manus_message', '') or msg.get('user_message', '')
        total_length += len(text)
    
    stats['content_analysis']['avg_length'] = total_length / len(messages) if messages else 0
    
    return stats


def display_statistics(stats):
    """Hiển thị thống kê đẹp mắt."""
    print(f"\n📊 THỐNG KÊ CHI TIẾT")
    print("=" * 70)
    
    # Message types
    print(f"📈 Phân loại theo Type:")
    total = stats['total']
    for msg_type, count in stats['by_type'].items():
        percentage = (count / total) * 100
        bar = "█" * int(percentage / 2)
        print(f"   {msg_type:12} │ {count:3d} ({percentage:5.1f}%) │ {bar}")
    
    # Manus subtypes
    if stats['by_subtype']:
        print(f"\n🏷️  Manus Message Subtypes:")
        manus_total = stats['by_type'].get('manus', 0)
        for subtype, count in stats['by_subtype'].items():
            percentage = (count / manus_total) * 100 if manus_total > 0 else 0
            bar = "▓" * int(percentage / 3)
            print(f"   {subtype:12} │ {count:3d} ({percentage:5.1f}%) │ {bar}")
    
    # Content analysis
    content = stats['content_analysis']
    print(f"\n📈 Content Analysis:")
    print(f"   📏 Average message length: {content['avg_length']:.0f} characters")
    print(f"   💻 Total code blocks: {content['total_code_blocks']}")
    print(f"   📋 Total list items: {content['total_list_items']}")
    
    if content['languages_detected']:
        print(f"   🔤 Programming languages: {', '.join(sorted(content['languages_detected']))}")
    
    if content['file_references']:
        file_refs = sorted(content['file_references'])[:10]  # Top 10
        print(f"   📁 File types detected: {', '.join(file_refs)}")


def display_examples_by_type(messages):
    """Hiển thị examples cho từng loại message."""
    print(f"\n💬 EXAMPLES BY MESSAGE TYPE")
    print("=" * 70)
    
    # Group messages by type and subtype
    examples = {}
    
    for msg in messages:
        msg_type = msg.get('type', 'unknown')
        
        if msg_type == 'manus':
            subtype = msg.get('message_subtype', 'unknown')
            key = f"manus_{subtype}"
        else:
            key = msg_type
        
        if key not in examples:
            examples[key] = []
        
        examples[key].append(msg)
    
    # Display examples
    for key, msgs in examples.items():
        if not msgs:
            continue
            
        print(f"\n🔸 {key.upper()} ({len(msgs)} messages)")
        print("-" * 50)
        
        # Show first example
        example = msgs[0]
        
        # Basic info
        print(f"Event ID: {example.get('event_id', 'N/A')}")
        
        if 'manus' in key:
            print(f"Confidence: {example.get('confidence', 'N/A')}")
            
            content_analysis = example.get('content_analysis', {})
            if content_analysis:
                print(f"Analysis: {content_analysis.get('text_length', 0)} chars, "
                      f"{content_analysis.get('code_blocks_count', 0)} code blocks, "
                      f"{content_analysis.get('list_items_count', 0)} list items")
        
        # Content preview
        text = example.get('manus_message', '') or example.get('user_message', '')
        preview = text[:200] + "..." if len(text) > 200 else text
        print(f"Content: {preview}")
        
        if len(msgs) > 1:
            print(f"... and {len(msgs) - 1} more similar messages")


def main():
    """Main function."""
    print("🎯 Starting Real Message Classification Demo")
    asyncio.run(demo_real_classification())
    print(f"\n🎉 Demo completed!")
    print(f"\n💡 Key Insights:")
    print(f"   • Hệ thống có thể phân loại messages từ HTML thực")
    print(f"   • Accuracy cao cho Manus messages (100% on tests)")
    print(f"   • Phát hiện được patterns phức tạp (mixed content)")
    print(f"   • Cần cải thiện detection cho user và task messages")
    print(f"\n📚 Xem thêm:")
    print(f"   • docs/MESSAGE_CLASSIFICATION.md - Hướng dẫn chi tiết")
    print(f"   • docs/MESSAGE_CLASSIFICATION_ANALYSIS.md - Phân tích từ HTML thực")


if __name__ == "__main__":
    main()
