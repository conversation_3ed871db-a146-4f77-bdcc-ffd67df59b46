@echo off
REM Manus Crawler Setup Script for Windows
echo 🚀 Manus Crawler - Setup Script (Windows)
echo ========================================

REM Check Python
echo 🐍 Checking Python...
python --version >nul 2>&1
if %errorlevel% == 0 (
    set PYTHON_CMD=python
    echo ✅ Python found
) else (
    python3 --version >nul 2>&1
    if %errorlevel% == 0 (
        set PYTHON_CMD=python3
        echo ✅ Python3 found
    ) else (
        echo ❌ Python not found! Please install Python 3.8+
        pause
        exit /b 1
    )
)

REM Check pip
echo 📦 Checking pip...
pip --version >nul 2>&1
if %errorlevel% == 0 (
    set PIP_CMD=pip
    echo ✅ pip found
) else (
    echo ❌ pip not found! Please install pip
    pause
    exit /b 1
)

REM Ask about virtual environment
set /p create_venv="🤔 Do you want to create a virtual environment? (y/n): "
if /i "%create_venv%"=="y" (
    echo 🏗️  Creating virtual environment...
    %PYTHON_CMD% -m venv venv
    call venv\Scripts\activate.bat
    echo ✅ Virtual environment activated
)

REM Install dependencies
echo 📦 Installing Python dependencies...
%PIP_CMD% install -r requirements.txt
if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)
echo ✅ Dependencies installed successfully

REM Install Playwright browsers
echo 🎭 Installing Playwright browsers...
playwright install chromium
if %errorlevel% neq 0 (
    echo ❌ Failed to install Playwright browsers
    pause
    exit /b 1
)
echo ✅ Playwright browsers installed successfully

REM Create chrome_profiles directory
echo 📁 Creating chrome_profiles directory...
if not exist chrome_profiles mkdir chrome_profiles
echo ✅ chrome_profiles directory created

REM Run tests
echo 🧪 Running tests...
%PYTHON_CMD% test_app.py
if %errorlevel% == 0 (
    echo ✅ All tests passed!
) else (
    echo ⚠️  Some tests failed, but you can still try to run the app
)

echo.
echo 🎉 Setup completed!
echo.
echo 🚀 To run the application:
echo    1. Start the server: %PYTHON_CMD% main.py
echo    2. Open browser: http://localhost:8000/ui
echo.
echo 🐳 Or use Docker:
echo    1. Build: docker-compose build
echo    2. Run: docker-compose up
echo    3. Open browser: http://localhost:8000/ui
echo.
echo 📚 API Documentation: http://localhost:8000/docs
echo.

REM Ask to start server
set /p start_server="🤔 Do you want to start the server now? (y/n): "
if /i "%start_server%"=="y" (
    echo 🚀 Starting Manus Crawler server...
    echo 📱 Open http://localhost:8000/ui in your browser
    echo 🛑 Press Ctrl+C to stop the server
    echo.
    %PYTHON_CMD% main.py
)

pause
