#!/bin/bash

# Manus Crawler Setup Script
echo "🚀 Manus Crawler - Setup Script"
echo "================================"

# Kiểm tra Python
echo "🐍 Checking Python..."
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
    echo "✅ Python3 found: $(python3 --version)"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
    echo "✅ Python found: $(python --version)"
else
    echo "❌ Python not found! Please install Python 3.8+"
    exit 1
fi

# Kiểm tra pip
echo "📦 Checking pip..."
if command -v pip3 &> /dev/null; then
    PIP_CMD="pip3"
    echo "✅ pip3 found"
elif command -v pip &> /dev/null; then
    PIP_CMD="pip"
    echo "✅ pip found"
else
    echo "❌ pip not found! Please install pip"
    exit 1
fi

# Tạo virtual environment (tùy chọn)
read -p "🤔 Do you want to create a virtual environment? (y/n): " create_venv
if [[ $create_venv == "y" || $create_venv == "Y" ]]; then
    echo "🏗️  Creating virtual environment..."
    $PYTHON_CMD -m venv venv
    
    # Activate virtual environment
    if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
        source venv/Scripts/activate
    else
        source venv/bin/activate
    fi
    echo "✅ Virtual environment activated"
fi

# Cài đặt dependencies
echo "📦 Installing Python dependencies..."
$PIP_CMD install -r requirements.txt

if [ $? -eq 0 ]; then
    echo "✅ Dependencies installed successfully"
else
    echo "❌ Failed to install dependencies"
    exit 1
fi

# Cài đặt Playwright browsers
echo "🎭 Installing Playwright browsers..."
playwright install chromium

if [ $? -eq 0 ]; then
    echo "✅ Playwright browsers installed successfully"
else
    echo "❌ Failed to install Playwright browsers"
    exit 1
fi

# Tạo thư mục chrome_profiles nếu chưa có
echo "📁 Creating chrome_profiles directory..."
mkdir -p chrome_profiles
echo "✅ chrome_profiles directory created"

# Chạy test
echo "🧪 Running tests..."
$PYTHON_CMD test_app.py

if [ $? -eq 0 ]; then
    echo "✅ All tests passed!"
else
    echo "⚠️  Some tests failed, but you can still try to run the app"
fi

echo ""
echo "🎉 Setup completed!"
echo ""
echo "🚀 To run the application:"
echo "   1. Start the server: $PYTHON_CMD main.py"
echo "   2. Open browser: http://localhost:8000/ui"
echo ""
echo "🐳 Or use Docker:"
echo "   1. Build: docker-compose build"
echo "   2. Run: docker-compose up"
echo "   3. Open browser: http://localhost:8000/ui"
echo ""
echo "📚 API Documentation: http://localhost:8000/docs"
echo ""

# Hỏi có muốn chạy ngay không
read -p "🤔 Do you want to start the server now? (y/n): " start_server
if [[ $start_server == "y" || $start_server == "Y" ]]; then
    echo "🚀 Starting Manus Crawler server..."
    echo "📱 Open http://localhost:8000/ui in your browser"
    echo "🛑 Press Ctrl+C to stop the server"
    echo ""
    $PYTHON_CMD main.py
fi
