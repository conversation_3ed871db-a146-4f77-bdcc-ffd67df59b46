#!/usr/bin/env python3
"""
Test đơn giản để kiểm tra API response có chứa message classification
"""

import json
import sys
from pathlib import Path

# Thêm thư mục root vào Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Import trự<PERSON> tiế<PERSON> để test
from app.core.message_classifier import ManusMessageClassifier


def test_classifier_directly():
    """Test classifier trực tiếp."""
    print("🧪 Testing ManusMessageClassifier directly...")
    
    # Test HTML đơn giản
    test_html = """
    <div class="prose">
        <p>I'll help you with Python programming. Here's a simple example:</p>
        <pre><code class="language-python">
def hello_world():
    print("Hello, World!")
    return "success"
        </code></pre>
        <p>This function demonstrates basic Python syntax.</p>
    </div>
    """
    
    test_text = "I'll help you with Python programming. Here's a simple example: def hello_world(): print(\"Hello, World!\") return \"success\" This function demonstrates basic Python syntax."
    
    try:
        result = ManusMessageClassifier.classify_message_type(
            manus_html=test_html,
            manus_text=test_text
        )
        
        print(f"✅ Classification successful!")
        print(f"   Message subtype: {result.get('message_subtype', 'N/A')}")
        print(f"   Confidence: {result.get('confidence', 'N/A')}")
        print(f"   Reasoning: {result.get('reasoning', 'N/A')}")
        
        content_analysis = result.get('content_analysis', {})
        if content_analysis:
            print(f"   Content analysis:")
            print(f"     - Text length: {content_analysis.get('text_length', 'N/A')}")
            print(f"     - Code blocks: {content_analysis.get('code_blocks_count', 'N/A')}")
            print(f"     - Languages: {content_analysis.get('detected_languages', [])}")
        
        # Test JSON serialization
        json_str = json.dumps(result, indent=2, ensure_ascii=False)
        print(f"   JSON serialization: ✅ Success ({len(json_str)} chars)")
        
        return True
        
    except Exception as e:
        print(f"❌ Classification failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def create_sample_api_response():
    """Tạo sample API response với message classification."""
    print("\n📝 Creating sample API response...")
    
    # Tạo sample response giống như API sẽ trả về
    sample_response = {
        "success": True,
        "data": {
            "page_title": "Test Page - Manus",
            "tasks": [],
            "current_task_title": "Test Task",
            "chat_messages": [
                {
                    "event_id": "user-msg-123",
                    "type": "user",
                    "user_message": "Can you help me with Python code?",
                    "timestamp": "2024-01-01 10:00"
                },
                {
                    "event_id": "manus-msg-456", 
                    "type": "manus",
                    "manus_message": "I'll help you with Python programming. Here's a simple example: def hello_world(): print(\"Hello, World!\") return \"success\" This function demonstrates basic Python syntax.",
                    "manus_html": "<div class=\"prose\"><p>I'll help you with Python programming. Here's a simple example:</p><pre><code class=\"language-python\">def hello_world():\n    print(\"Hello, World!\")\n    return \"success\"</code></pre><p>This function demonstrates basic Python syntax.</p></div>",
                    "timestamp": "2024-01-01 10:01",
                    # Thông tin phân loại message
                    "message_subtype": "code",
                    "content_analysis": {
                        "has_content": True,
                        "text_length": 150,
                        "html_length": 300,
                        "has_code_blocks": True,
                        "code_blocks_count": 1,
                        "detected_languages": ["python"],
                        "has_lists": False,
                        "list_items_count": 0,
                        "file_references": []
                    }
                }
            ],
            "footer_user": {},
            "profile_status": {
                "profile_name": None,
                "use_system_profile": False,
                "headless": True
            }
        },
        "message": "Crawl thành công"
    }
    
    try:
        # Test JSON serialization
        json_str = json.dumps(sample_response, indent=2, ensure_ascii=False)
        print(f"✅ Sample response created successfully!")
        print(f"   JSON size: {len(json_str)} characters")
        
        # Hiển thị structure
        print(f"\n📊 Response structure:")
        print(f"   - Success: {sample_response['success']}")
        print(f"   - Chat messages: {len(sample_response['data']['chat_messages'])}")
        
        for i, msg in enumerate(sample_response['data']['chat_messages'], 1):
            print(f"   - Message {i}:")
            print(f"     * Type: {msg['type']}")
            if msg['type'] == 'manus':
                print(f"     * Subtype: {msg.get('message_subtype', 'N/A')}")
                print(f"     * Has content analysis: {'Yes' if msg.get('content_analysis') else 'No'}")
        
        # Lưu sample response
        output_file = Path(__file__).parent.parent / "data" / "sample_api_response.json"
        output_file.parent.mkdir(exist_ok=True)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(json_str)
        
        print(f"   📁 Saved to: {output_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to create sample response: {str(e)}")
        return False


def verify_schema_compatibility():
    """Kiểm tra schema compatibility."""
    print("\n🔧 Verifying schema compatibility...")
    
    try:
        from app.models.schemas import ChatMessage
        
        # Test tạo ChatMessage với fields mới
        test_message = ChatMessage(
            event_id="test-123",
            type="manus",
            manus_message="Test message",
            manus_html="<p>Test</p>",
            timestamp="2024-01-01 10:00",
            message_subtype="code",
            content_analysis={
                "text_length": 100,
                "code_blocks_count": 1,
                "detected_languages": ["python"]
            }
        )
        
        print(f"✅ ChatMessage schema compatible!")
        print(f"   Event ID: {test_message.event_id}")
        print(f"   Type: {test_message.type}")
        print(f"   Message subtype: {test_message.message_subtype}")
        print(f"   Content analysis: {'Present' if test_message.content_analysis else 'Missing'}")
        
        # Test JSON serialization
        json_data = test_message.model_dump()
        json_str = json.dumps(json_data, ensure_ascii=False)
        print(f"   JSON serialization: ✅ Success ({len(json_str)} chars)")
        
        return True
        
    except Exception as e:
        print(f"❌ Schema compatibility failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test function."""
    print("🚀 Testing API Response with Message Classification")
    print("=" * 60)
    
    # Test 1: Classifier trực tiếp
    test1_success = test_classifier_directly()
    
    # Test 2: Sample API response
    test2_success = create_sample_api_response()
    
    # Test 3: Schema compatibility
    test3_success = verify_schema_compatibility()
    
    print(f"\n📊 Test Results:")
    print(f"   Classifier test: {'✅ PASS' if test1_success else '❌ FAIL'}")
    print(f"   Sample response: {'✅ PASS' if test2_success else '❌ FAIL'}")
    print(f"   Schema compatibility: {'✅ PASS' if test3_success else '❌ FAIL'}")
    
    if all([test1_success, test2_success, test3_success]):
        print(f"\n🎉 All tests passed!")
        print(f"\n💡 Kết luận:")
        print(f"   • Message classifier hoạt động đúng")
        print(f"   • API response sẽ chứa message_subtype và content_analysis")
        print(f"   • Schema đã được cập nhật để support classification")
        print(f"   • JSON serialization hoạt động tốt")
        
        print(f"\n📋 Cách sử dụng:")
        print(f"   1. Gọi API /crawl-html/ hoặc /crawl-url/")
        print(f"   2. Kiểm tra response.data.chat_messages[].message_subtype")
        print(f"   3. Sử dụng response.data.chat_messages[].content_analysis cho chi tiết")
        
        return True
    else:
        print(f"\n⚠️  Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
