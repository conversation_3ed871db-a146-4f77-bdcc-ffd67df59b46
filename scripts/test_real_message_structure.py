#!/usr/bin/env python3
"""
Test script để kiểm tra với HTML structure thực tế từ user
"""

import sys
import asyncio
import json
from pathlib import Path

# Thêm thư mục root vào Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.core.crawler import crawl_manus_page_content


async def test_real_message_structure():
    """Test với HTML structure thực tế từ user."""
    print("🧪 Testing với HTML structure thực tế từ user")
    print("=" * 60)
    
    # HTML structure thực tế từ user
    real_html = """
    <html>
        <head><title>Manus AI - Real Test</title></head>
        <body>
            <div data-event-id="h6LIG2tPotM3mHkIegZTOC">
                <div class="mb-4 last:mb-0 whitespace-pre-wrap u-break-words">
                    Tôi hiểu vấn đề của bạn. Bạn muốn mã hoạt động đúng mà không cần sử dụng tham số <code>skip_login_check</code>. Tôi sẽ sửa lại mã để đảm bảo toàn bộ flow hoạt động chính xác mà không cần tham số này.
                </div>
            </div>
            
            <!-- Thêm một message khác để test -->
            <div data-event-id="test-code-message">
                <div class="mb-4 last:mb-0 whitespace-pre-wrap u-break-words">
                    Đây là code Python để fix vấn đề:
                    <pre><code class="language-python">
def check_login_status(page):
    try:
        # Kiểm tra xem có đăng nhập không
        login_indicator = page.query_selector("div.user-profile")
        return login_indicator is not None
    except Exception:
        return False
                    </code></pre>
                    Code này sẽ kiểm tra trạng thái đăng nhập một cách chính xác.
                </div>
            </div>
            
            <!-- Message với list -->
            <div data-event-id="test-list-message">
                <div class="mb-4 last:mb-0 whitespace-pre-wrap u-break-words">
                    Các bước để fix vấn đề:
                    1. Kiểm tra selector đăng nhập
                    2. Cập nhật logic check login
                    3. Test với các trường hợp khác nhau
                    4. Deploy code mới
                    
                    Bạn cần thực hiện từng bước một cách cẩn thận.
                </div>
            </div>
            
            <!-- Message với file references -->
            <div data-event-id="test-file-message">
                <div class="mb-4 last:mb-0 whitespace-pre-wrap u-break-words">
                    Bạn cần cập nhật các file sau:
                    - crawler.py: Logic crawl chính
                    - selectors.json: CSS selectors
                    - main.py: API endpoints
                    - requirements.txt: Dependencies
                    - config.yaml: Configuration
                    
                    Đảm bảo backup trước khi thay đổi.
                </div>
            </div>
        </body>
    </html>
    """
    
    print(f"📄 Testing với real HTML structure")
    print(f"📊 HTML size: {len(real_html):,} characters")
    
    try:
        # Test crawl
        result = await crawl_manus_page_content(
            html_content=real_html,
            headless=True
        )
        
        if not result["success"]:
            print(f"❌ Crawl failed: {result.get('message', 'Unknown error')}")
            return False
        
        print(f"✅ Crawl successful!")
        
        # Phân tích response
        data = result.get("data", {})
        chat_messages = data.get("chat_messages", [])
        
        print(f"\n📊 Response Analysis:")
        print(f"   - Success: {result['success']}")
        print(f"   - Page title: {data.get('page_title', 'N/A')}")
        print(f"   - Total messages found: {len(chat_messages)}")
        
        if len(chat_messages) == 0:
            print(f"❌ No messages found! Selector issue detected.")
            return False
        
        # Kiểm tra từng message
        print(f"\n💬 Message Details:")
        
        classification_found = False
        
        for i, msg in enumerate(chat_messages, 1):
            event_id = msg.get("event_id", "N/A")
            msg_type = msg.get("type", "unknown")
            
            print(f"\n   Message {i} (ID: {event_id}):")
            print(f"     Type: {msg_type}")
            
            if msg_type == "manus":
                # Kiểm tra content
                manus_message = msg.get("manus_message", "")
                manus_html = msg.get("manus_html", "")
                
                print(f"     Content: {manus_message[:100]}{'...' if len(manus_message) > 100 else ''}")
                print(f"     HTML length: {len(manus_html)} chars")
                
                # Kiểm tra classification fields
                message_subtype = msg.get("message_subtype")
                content_analysis = msg.get("content_analysis")
                
                if message_subtype:
                    print(f"     🏷️  Subtype: {message_subtype}")
                    classification_found = True
                else:
                    print(f"     ❌ Missing message_subtype!")
                
                if content_analysis:
                    print(f"     📈 Content Analysis: Present")
                    print(f"        - Text length: {content_analysis.get('text_length', 'N/A')}")
                    print(f"        - Code blocks: {content_analysis.get('code_blocks_count', 0)}")
                    print(f"        - List items: {content_analysis.get('list_items_count', 0)}")
                    
                    languages = content_analysis.get('detected_languages', [])
                    if languages:
                        print(f"        - Languages: {', '.join(languages)}")
                    
                    file_refs = content_analysis.get('file_references', [])
                    if file_refs:
                        print(f"        - File refs: {', '.join(file_refs[:3])}")
                else:
                    print(f"     ❌ Missing content_analysis!")
            
            else:
                print(f"     Content: {msg.get('user_message', 'N/A')}")
        
        # Test JSON serialization
        print(f"\n🔧 JSON Serialization Test:")
        try:
            json_str = json.dumps(result, indent=2, ensure_ascii=False)
            print(f"   ✅ Success - {len(json_str):,} characters")
            
            # Lưu để inspection
            output_file = Path(__file__).parent.parent / "data" / "real_structure_test.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(json_str)
            print(f"   📁 Saved to: {output_file}")
            
        except Exception as json_error:
            print(f"   ❌ JSON serialization failed: {str(json_error)}")
            return False
        
        # Kết luận
        print(f"\n🎯 Test Results:")
        print(f"   Messages found: {len(chat_messages)}")
        print(f"   Classification working: {'✅ Yes' if classification_found else '❌ No'}")
        
        if classification_found:
            print(f"   🎉 Message classification is working with real structure!")
            return True
        else:
            print(f"   ⚠️  Message classification not working - need to debug selectors")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def debug_selectors():
    """Debug selectors để tìm vấn đề."""
    print(f"\n🔍 Debugging Selectors...")
    print("=" * 40)
    
    from app.core import selectors as sel
    
    print(f"Current selectors:")
    print(f"   CHAT_EVENT_CONTAINER_CSS: {sel.CHAT_EVENT_CONTAINER_CSS}")
    print(f"   MANUS_MESSAGE_CONTENT_PROSE_CSS: {sel.MANUS_MESSAGE_CONTENT_PROSE_CSS}")
    print(f"   USER_MESSAGE_TEXT_CSS: {sel.USER_MESSAGE_TEXT_CSS}")
    
    # Test HTML fragment
    test_html = """
    <div data-event-id="h6LIG2tPotM3mHkIegZTOC">
        <div class="mb-4 last:mb-0 whitespace-pre-wrap u-break-words">
            Test message content
        </div>
    </div>
    """
    
    from bs4 import BeautifulSoup
    soup = BeautifulSoup(test_html, 'html.parser')
    
    # Test selectors
    print(f"\n🧪 Testing selectors on sample HTML:")
    
    # Test event container
    event_containers = soup.select('div[data-event-id]')
    print(f"   Event containers found: {len(event_containers)}")
    
    # Test manus message selector
    manus_selectors = [
        'div.prose',
        'div.mb-4.last\\:mb-0.whitespace-pre-wrap.u-break-words',
        'div.mb-4',
        '.whitespace-pre-wrap',
        '.u-break-words'
    ]
    
    for selector in manus_selectors:
        try:
            elements = soup.select(selector)
            print(f"   Selector '{selector}': {len(elements)} matches")
        except Exception as e:
            print(f"   Selector '{selector}': ERROR - {str(e)}")


async def main():
    """Main test function."""
    print("🚀 Testing Real Message Structure")
    
    # Debug selectors first
    await debug_selectors()
    
    # Test with real structure
    success = await test_real_message_structure()
    
    if success:
        print(f"\n🎉 Test completed successfully!")
        print(f"   Message classification is working with real HTML structure.")
    else:
        print(f"\n⚠️  Test revealed issues that need to be fixed.")
        print(f"   Please check selectors and classification logic.")
    
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
