#!/usr/bin/env python3
"""
Test script để kiểm tra API response có chứa thông tin phân loại message không
"""

import sys
import asyncio
import json
from pathlib import Path

# Thêm thư mục root vào Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.core.crawler import crawl_manus_page_content


async def test_api_response_format():
    """Test format của API response."""
    print("🧪 Testing API Response Format for Message Classification")
    print("=" * 60)
    
    # Test HTML với message đơn giản
    test_html = """
    <html>
        <body>
            <div data-event-id="test-user-123">
                <div class="flex w-full flex-col items-end justify-end gap-1 group mt-3">
                    <span class="text-[var(--text-primary)] u-break-words whitespace-pre-wrap">
                        Hello, can you help me with Python code?
                    </span>
                </div>
            </div>
            
            <div data-event-id="test-manus-456">
                <div class="flex flex-col gap-2 w-full group mt-3">
                    <div class="prose prose-sm sm:prose-base dark:prose-invert">
                        <div class="mb-4 last:mb-0 whitespace-pre-wrap u-break-words">
                            I'll help you with Python code. Here's an example:
                        </div>
                        <pre><code class="language-python">
def hello_world():
    print("Hello, World!")
    return "success"
                        </code></pre>
                        <div class="mb-4 last:mb-0 whitespace-pre-wrap u-break-words">
                            This function demonstrates basic Python syntax.
                        </div>
                    </div>
                </div>
            </div>
        </body>
    </html>
    """
    
    try:
        # Gọi crawler function
        result = await crawl_manus_page_content(
            html_content=test_html,
            headless=True
        )
        
        print(f"✅ Crawl completed successfully: {result['success']}")
        
        if not result['success']:
            print(f"❌ Crawl failed: {result.get('message', 'Unknown error')}")
            return False
        
        # Kiểm tra structure của response
        data = result.get('data', {})
        chat_messages = data.get('chat_messages', [])
        
        print(f"\n📊 Response Structure:")
        print(f"   - Success: {result['success']}")
        print(f"   - Data keys: {list(data.keys())}")
        print(f"   - Chat messages count: {len(chat_messages)}")
        
        # Kiểm tra từng message
        print(f"\n💬 Message Analysis:")
        
        for i, msg in enumerate(chat_messages, 1):
            print(f"\n   Message {i}:")
            print(f"     Event ID: {msg.get('event_id', 'N/A')}")
            print(f"     Type: {msg.get('type', 'N/A')}")
            
            # Kiểm tra các field mới
            message_subtype = msg.get('message_subtype')
            content_analysis = msg.get('content_analysis')
            
            print(f"     Message Subtype: {message_subtype}")
            print(f"     Content Analysis: {'Present' if content_analysis else 'Missing'}")
            
            if message_subtype:
                print(f"     ✅ Message classification present")
            else:
                print(f"     ❌ Message classification MISSING")
            
            if content_analysis:
                print(f"     ✅ Content analysis present")
                print(f"        - Text length: {content_analysis.get('text_length', 'N/A')}")
                print(f"        - Code blocks: {content_analysis.get('code_blocks_count', 'N/A')}")
                print(f"        - Languages: {content_analysis.get('detected_languages', [])}")
            else:
                print(f"     ❌ Content analysis MISSING")
            
            # Preview content
            content = msg.get('manus_message', '') or msg.get('user_message', '')
            preview = content[:100] + "..." if len(content) > 100 else content
            print(f"     Content: {preview}")
        
        # Kiểm tra JSON serialization
        print(f"\n🔧 JSON Serialization Test:")
        try:
            json_str = json.dumps(result, indent=2, ensure_ascii=False)
            print(f"   ✅ JSON serialization successful")
            print(f"   📏 JSON size: {len(json_str)} characters")
            
            # Parse lại để đảm bảo
            parsed = json.loads(json_str)
            print(f"   ✅ JSON parsing successful")
            
            # Kiểm tra message classification trong JSON
            parsed_messages = parsed.get('data', {}).get('chat_messages', [])
            classified_count = sum(1 for msg in parsed_messages if msg.get('message_subtype'))
            
            print(f"   📊 Classified messages in JSON: {classified_count}/{len(parsed_messages)}")
            
        except Exception as json_error:
            print(f"   ❌ JSON serialization failed: {str(json_error)}")
            return False
        
        # Summary
        total_messages = len(chat_messages)
        classified_messages = sum(1 for msg in chat_messages if msg.get('message_subtype'))
        
        print(f"\n📈 Summary:")
        print(f"   Total messages: {total_messages}")
        print(f"   Classified messages: {classified_messages}")
        print(f"   Classification rate: {(classified_messages/total_messages*100):.1f}%" if total_messages > 0 else "   Classification rate: N/A")
        
        if classified_messages == total_messages and total_messages > 0:
            print(f"   🎉 All messages have classification data!")
            return True
        elif classified_messages > 0:
            print(f"   ⚠️  Partial classification - some messages missing data")
            return False
        else:
            print(f"   ❌ No messages have classification data!")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def test_with_real_html():
    """Test với HTML thực từ file example."""
    print(f"\n🧪 Testing with Real HTML Data")
    print("=" * 60)
    
    html_file = Path(__file__).parent.parent / "data" / "html-manus.example.html"
    
    if not html_file.exists():
        print(f"❌ File không tồn tại: {html_file}")
        return False
    
    with open(html_file, 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    print(f"📄 Testing with real HTML file: {html_file.name}")
    print(f"📊 File size: {len(html_content):,} characters")
    
    try:
        result = await crawl_manus_page_content(
            html_content=html_content,
            headless=True
        )
        
        if not result['success']:
            print(f"❌ Crawl failed: {result.get('message', 'Unknown error')}")
            return False
        
        data = result.get('data', {})
        chat_messages = data.get('chat_messages', [])
        
        print(f"✅ Crawl successful - found {len(chat_messages)} messages")
        
        # Phân tích classification
        classification_stats = {}
        for msg in chat_messages:
            msg_type = msg.get('type', 'unknown')
            subtype = msg.get('message_subtype', 'unclassified')
            
            key = f"{msg_type}_{subtype}" if subtype != 'unclassified' else msg_type
            classification_stats[key] = classification_stats.get(key, 0) + 1
        
        print(f"\n📊 Classification Statistics:")
        for key, count in classification_stats.items():
            percentage = (count / len(chat_messages)) * 100
            print(f"   {key}: {count} ({percentage:.1f}%)")
        
        # Kiểm tra có message nào có classification không
        classified_count = sum(1 for msg in chat_messages if msg.get('message_subtype'))
        
        print(f"\n📈 Real Data Summary:")
        print(f"   Total messages: {len(chat_messages)}")
        print(f"   Messages with classification: {classified_count}")
        print(f"   Classification coverage: {(classified_count/len(chat_messages)*100):.1f}%" if chat_messages else "   Classification coverage: N/A")
        
        return classified_count > 0
        
    except Exception as e:
        print(f"❌ Real HTML test failed: {str(e)}")
        return False


async def main():
    """Main test function."""
    print("🚀 Starting API Response Format Tests")
    print("=" * 60)
    
    # Test 1: Simple HTML
    test1_success = await test_api_response_format()
    
    # Test 2: Real HTML
    test2_success = await test_with_real_html()
    
    print(f"\n📊 Final Results:")
    print(f"   Simple HTML test: {'✅ PASS' if test1_success else '❌ FAIL'}")
    print(f"   Real HTML test: {'✅ PASS' if test2_success else '❌ FAIL'}")
    
    if test1_success and test2_success:
        print(f"\n🎉 All tests passed! API response includes message classification.")
        return True
    else:
        print(f"\n⚠️  Some tests failed. API response may be missing classification data.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
