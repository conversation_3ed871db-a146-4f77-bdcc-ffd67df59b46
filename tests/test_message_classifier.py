#!/usr/bin/env python3
"""
Test script cho Message Classifier
"""

import sys
import os
from pathlib import Path

# Thêm thư mục root vào Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.core.message_classifier import ManusMessageClassifier


def test_text_message():
    """Test phân loại message text thông thường."""
    print("🧪 Testing text message classification...")
    
    html_content = """
    <div class="prose">
        <p>Tôi sẽ giúp bạn tạo một ứng dụng web scraper cho Manus.im. 
        Đây là một dự án thú vị và tôi sẽ hướng dẫn bạn từng bước.</p>
        <p>Chúng ta sẽ sử dụng Python với Playwright và FastAPI để tạo một giải pháp hoàn chỉnh.</p>
    </div>
    """
    
    text_content = "Tôi sẽ giúp bạn tạo một ứng dụng web scraper cho Manus.im. Đây là một dự án thú vị và tôi sẽ hướng dẫn bạn từng bước. Chúng ta sẽ sử dụng Python với Playwright và FastAPI để tạo một giải pháp hoàn chỉnh."
    
    result = ManusMessageClassifier.classify_message_type(html_content, text_content)
    
    print(f"   Message subtype: {result['message_subtype']}")
    print(f"   Confidence: {result['confidence']}")
    print(f"   Reasoning: {result['reasoning']}")
    
    assert result['message_subtype'] == 'text'
    print("   ✅ Text message classification passed!")
    return True


def test_code_message():
    """Test phân loại message chứa code."""
    print("\n🧪 Testing code message classification...")
    
    html_content = """
    <div class="prose">
        <p>Đây là code Python để tạo web scraper:</p>
        <pre><code class="language-python">
import asyncio
from playwright.async_api import async_playwright

async def crawl_page():
    async with async_playwright() as p:
        browser = await p.chromium.launch()
        page = await browser.new_page()
        await page.goto("https://manus.im")
        return await page.content()
        </code></pre>
        <p>Bạn có thể chạy code này để bắt đầu crawl.</p>
    </div>
    """
    
    text_content = """Đây là code Python để tạo web scraper:

import asyncio
from playwright.async_api import async_playwright

async def crawl_page():
    async with async_playwright() as p:
        browser = await p.chromium.launch()
        page = await browser.new_page()
        await page.goto("https://manus.im")
        return await page.content()

Bạn có thể chạy code này để bắt đầu crawl."""
    
    result = ManusMessageClassifier.classify_message_type(html_content, text_content)
    
    print(f"   Message subtype: {result['message_subtype']}")
    print(f"   Confidence: {result['confidence']}")
    print(f"   Reasoning: {result['reasoning']}")
    print(f"   Detected languages: {result['content_analysis'].get('detected_languages', [])}")
    
    assert result['message_subtype'] == 'code'
    print("   ✅ Code message classification passed!")
    return True


def test_list_message():
    """Test phân loại message chứa lists/steps."""
    print("\n🧪 Testing list message classification...")
    
    html_content = """
    <div class="prose">
        <h3>Các bước để tạo web scraper:</h3>
        <ol>
            <li>Cài đặt Playwright</li>
            <li>Tạo file crawler.py</li>
            <li>Thiết lập selectors</li>
            <li>Viết logic crawl</li>
            <li>Test và debug</li>
        </ol>
        <p>Bước đầu tiên là quan trọng nhất.</p>
    </div>
    """
    
    text_content = """Các bước để tạo web scraper:

1. Cài đặt Playwright
2. Tạo file crawler.py
3. Thiết lập selectors
4. Viết logic crawl
5. Test và debug

Bước đầu tiên là quan trọng nhất."""
    
    result = ManusMessageClassifier.classify_message_type(html_content, text_content)
    
    print(f"   Message subtype: {result['message_subtype']}")
    print(f"   Confidence: {result['confidence']}")
    print(f"   Reasoning: {result['reasoning']}")
    print(f"   Has lists: {result['content_analysis'].get('has_lists', False)}")
    print(f"   Step indicators: {result['content_analysis'].get('step_indicators', [])}")
    
    assert result['message_subtype'] == 'list'
    print("   ✅ List message classification passed!")
    return True


def test_mixed_message():
    """Test phân loại message có nội dung hỗn hợp."""
    print("\n🧪 Testing mixed message classification...")
    
    html_content = """
    <div class="prose">
        <h2>Hướng dẫn tạo Web Scraper</h2>
        <p>Tôi sẽ hướng dẫn bạn tạo một web scraper hoàn chỉnh cho Manus.im.</p>
        
        <h3>Bước 1: Cài đặt dependencies</h3>
        <pre><code class="language-bash">pip install playwright fastapi uvicorn</code></pre>
        
        <h3>Bước 2: Tạo file crawler.py</h3>
        <pre><code class="language-python">
import asyncio
from playwright.async_api import async_playwright

class ManusWebScraper:
    def __init__(self):
        self.base_url = "https://manus.im"
        </code></pre>
        
        <h3>Các file cần tạo:</h3>
        <ul>
            <li>crawler.py - Logic crawl chính</li>
            <li>selectors.py - CSS selectors</li>
            <li>main.py - FastAPI server</li>
            <li>requirements.txt - Dependencies</li>
        </ul>
        
        <p>Đây là một dự án phức tạp với nhiều thành phần khác nhau.</p>
    </div>
    """
    
    text_content = """Hướng dẫn tạo Web Scraper

Tôi sẽ hướng dẫn bạn tạo một web scraper hoàn chỉnh cho Manus.im.

Bước 1: Cài đặt dependencies
pip install playwright fastapi uvicorn

Bước 2: Tạo file crawler.py
import asyncio
from playwright.async_api import async_playwright

class ManusWebScraper:
    def __init__(self):
        self.base_url = "https://manus.im"

Các file cần tạo:
- crawler.py - Logic crawl chính
- selectors.py - CSS selectors  
- main.py - FastAPI server
- requirements.txt - Dependencies

Đây là một dự án phức tạp với nhiều thành phần khác nhau."""
    
    result = ManusMessageClassifier.classify_message_type(html_content, text_content)
    
    print(f"   Message subtype: {result['message_subtype']}")
    print(f"   Confidence: {result['confidence']}")
    print(f"   Reasoning: {result['reasoning']}")
    print(f"   Has code blocks: {result['content_analysis'].get('has_code_blocks', False)}")
    print(f"   Has lists: {result['content_analysis'].get('has_lists', False)}")
    print(f"   Has headings: {result['content_analysis'].get('has_headings', False)}")
    print(f"   Code blocks count: {result['content_analysis'].get('code_blocks_count', 0)}")
    
    assert result['message_subtype'] == 'mixed'
    print("   ✅ Mixed message classification passed!")
    return True


def test_file_message():
    """Test phân loại message chứa file references."""
    print("\n🧪 Testing file message classification...")
    
    html_content = """
    <div class="prose">
        <p>Bạn cần tạo các file sau:</p>
        <p>File crawler.py sẽ chứa logic chính. File selectors.py sẽ chứa CSS selectors. 
        File requirements.txt sẽ list dependencies. File docker-compose.yml để containerize.</p>
        <p>Ngoài ra còn có package.json cho frontend và config.yaml cho settings.</p>
    </div>
    """
    
    text_content = """Bạn cần tạo các file sau:

File crawler.py sẽ chứa logic chính. File selectors.py sẽ chứa CSS selectors. 
File requirements.txt sẽ list dependencies. File docker-compose.yml để containerize.

Ngoài ra còn có package.json cho frontend và config.yaml cho settings."""
    
    result = ManusMessageClassifier.classify_message_type(html_content, text_content)
    
    print(f"   Message subtype: {result['message_subtype']}")
    print(f"   Confidence: {result['confidence']}")
    print(f"   Reasoning: {result['reasoning']}")
    print(f"   File references: {result['content_analysis'].get('file_references', [])}")
    
    assert result['message_subtype'] == 'file'
    print("   ✅ File message classification passed!")
    return True


def main():
    """Chạy tất cả tests."""
    print("🚀 Starting Message Classifier Tests...\n")
    
    tests = [
        test_text_message,
        test_code_message,
        test_list_message,
        test_mixed_message,
        test_file_message,
    ]
    
    passed = 0
    failed = 0
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"   ❌ Test failed: {str(e)}")
            failed += 1
    
    print(f"\n📊 Test Results:")
    print(f"   ✅ Passed: {passed}")
    print(f"   ❌ Failed: {failed}")
    print(f"   📈 Success rate: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 All tests passed! Message classifier is working correctly.")
        return True
    else:
        print(f"\n⚠️  {failed} test(s) failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
