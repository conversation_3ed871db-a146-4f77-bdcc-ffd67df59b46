#!/usr/bin/env python3
"""
Test script để kiểm tra fix cho lỗi SingletonLock
"""

import asyncio
import httpx
import json
import uuid

async def test_profile_crawl():
    """Test crawl với profile để kiểm tra fix SingletonLock."""
    
    print("🧪 Testing Profile Crawl Fix")
    print("=" * 50)
    
    # Tạo request ID
    request_id = str(uuid.uuid4())
    print(f"📋 Request ID: {request_id}")
    
    # Test data
    test_data = {
        "url": "https://manus.im/",
        "request_id": request_id,
        "profile_name": "manus_login_profile",
        "headless": True
    }
    
    print(f"🎭 Testing with profile: {test_data['profile_name']}")
    print(f"🌐 URL: {test_data['url']}")
    print(f"👁️  Headless: {test_data['headless']}")
    
    async with httpx.AsyncClient(timeout=60.0) as client:
        try:
            print("\n📡 Sending crawl request...")
            response = await client.post(
                "http://localhost:8000/crawl-url-realtime/",
                json=test_data
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Request sent successfully!")
                print(f"📝 Response: {result}")
                
                print(f"\n⏳ Waiting for crawl to complete...")
                print(f"💡 Check the main UI at http://localhost:8000/ui for realtime updates")
                print(f"🔗 WebSocket should connect to: ws://localhost:8000/ws/crawl-status/{request_id}")
                
                return True
                
            else:
                print(f"❌ Request failed: {response.status_code}")
                print(f"📄 Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Error: {str(e)}")
            return False

async def test_html_crawl():
    """Test crawl với HTML content để đảm bảo basic functionality vẫn hoạt động."""
    
    print("\n🧪 Testing HTML Crawl (Basic)")
    print("=" * 50)
    
    # Tạo request ID
    request_id = str(uuid.uuid4())
    
    # Test HTML content
    html_content = """
    <html>
        <head><title>Test Manus Page</title></head>
        <body>
            <div class="group flex h-14">
                <img src="/icon.png" alt="Task Icon">
                <span class="truncate text-sm font-medium" title="Test Task">Test Task</span>
                <span class="text-xs whitespace-nowrap">10:30</span>
            </div>
        </body>
    </html>
    """
    
    test_data = {
        "html_content": html_content,
        "request_id": request_id,
        "profile_name": "test_profile",
        "headless": True
    }
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        try:
            print("📡 Sending HTML crawl request...")
            response = await client.post(
                "http://localhost:8000/crawl-html-realtime/",
                json=test_data
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ HTML crawl request sent successfully!")
                print(f"📝 Response: {result}")
                return True
                
            else:
                print(f"❌ HTML crawl failed: {response.status_code}")
                print(f"📄 Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ HTML crawl error: {str(e)}")
            return False

async def main():
    """Main test function."""
    
    print("🚀 Profile Fix Test Suite")
    print("=" * 50)
    print("Testing fixes for SingletonLock error")
    print()
    
    # Test 1: HTML crawl (should always work)
    html_success = await test_html_crawl()
    
    # Test 2: Profile crawl (the one that was failing)
    profile_success = await test_profile_crawl()
    
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS:")
    print("=" * 50)
    
    print(f"HTML Crawl: {'✅ PASSED' if html_success else '❌ FAILED'}")
    print(f"Profile Crawl: {'✅ PASSED' if profile_success else '❌ FAILED'}")
    
    if html_success and profile_success:
        print("\n🎉 All tests passed! SingletonLock fix is working!")
        print("\n💡 What was fixed:")
        print("   ✅ Automatic cleanup of Chrome lock files")
        print("   ✅ Kill existing Chrome processes for profile")
        print("   ✅ Retry mechanism with better error handling")
        print("   ✅ Additional Chrome args for stability")
        
        print("\n🔧 Technical details:")
        print("   - cleanup_profile_locks() removes SingletonLock files")
        print("   - kill_chrome_processes_for_profile() kills existing processes")
        print("   - Retry logic with 2-second delay")
        print("   - Better Chrome launch arguments")
        
    else:
        print("\n⚠️  Some tests failed. Check the errors above.")
        
        if not profile_success:
            print("\n🔧 Troubleshooting steps:")
            print("   1. Make sure no Chrome instances are using the profile")
            print("   2. Check chrome_profiles directory permissions")
            print("   3. Try deleting the profile directory and recreating")
            print("   4. Check if Playwright is properly installed")

if __name__ == "__main__":
    asyncio.run(main())
