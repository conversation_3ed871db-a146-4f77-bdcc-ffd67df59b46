#!/usr/bin/env python3
"""
Test script để test message classification với dữ liệu HTML thực
"""

import sys
import os
import asyncio
from pathlib import Path

# Thêm thư mục root vào Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.core.crawler import crawl_manus_page_content


async def test_real_html_classification():
    """Test phân loại message với HTML thực từ file example."""
    print("🧪 Testing message classification with real HTML data...")
    
    # Đọc file HTML example
    html_file = Path(__file__).parent.parent / "data" / "html-manus.example.html"
    
    if not html_file.exists():
        print(f"❌ File {html_file} không tồn tại!")
        return False
    
    with open(html_file, 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    print(f"📄 Đã đọc file {html_file.name} ({len(html_content)} characters)")
    
    try:
        # Test crawl HTML với message classification
        result = await crawl_manus_page_content(
            html_content=html_content,
            headless=True
        )
        
        if not result["success"]:
            print(f"❌ Crawl thất bại: {result.get('message', 'Unknown error')}")
            return False
        
        data = result["data"]
        chat_messages = data.get('chat_messages', [])
        
        print(f"✅ Crawl thành công!")
        print(f"📊 Tổng quan:")
        print(f"   - Page title: {data.get('page_title', 'N/A')}")
        print(f"   - Tasks found: {len(data.get('tasks', []))}")
        print(f"   - Chat messages: {len(chat_messages)}")
        print(f"   - Current task title: {data.get('current_task_title', 'N/A')}")
        
        # Phân tích chi tiết các message
        print(f"\n💬 Chi tiết phân loại messages:")
        
        user_messages = 0
        manus_messages = 0
        message_types = {}
        
        for i, msg in enumerate(chat_messages, 1):
            print(f"\n   Message {i}:")
            print(f"     Event ID: {msg.get('event_id', 'N/A')}")
            print(f"     Type: {msg.get('type', 'N/A')}")
            print(f"     Timestamp: {msg.get('timestamp', 'N/A')}")
            
            if msg.get('type') == 'user':
                user_messages += 1
                user_text = msg.get('user_message', '')
                print(f"     User message: {user_text[:100]}{'...' if len(user_text) > 100 else ''}")
            
            elif msg.get('type') == 'manus':
                manus_messages += 1
                manus_text = msg.get('manus_message', '')
                message_subtype = msg.get('message_subtype', 'unknown')
                content_analysis = msg.get('content_analysis', {})
                
                print(f"     Manus message: {manus_text[:100]}{'...' if len(manus_text) > 100 else ''}")
                print(f"     🏷️  Message subtype: {message_subtype}")
                
                # Đếm các loại message
                if message_subtype in message_types:
                    message_types[message_subtype] += 1
                else:
                    message_types[message_subtype] = 1
                
                # Hiển thị thông tin phân tích nội dung
                if content_analysis:
                    print(f"     📈 Content analysis:")
                    if content_analysis.get('has_code_blocks'):
                        print(f"        - Code blocks: {content_analysis.get('code_blocks_count', 0)}")
                        if content_analysis.get('detected_languages'):
                            print(f"        - Languages: {', '.join(content_analysis.get('detected_languages', []))}")
                    
                    if content_analysis.get('has_lists'):
                        print(f"        - List items: {content_analysis.get('list_items_count', 0)}")
                    
                    if content_analysis.get('has_headings'):
                        print(f"        - Headings: {content_analysis.get('headings_count', 0)}")
                    
                    if content_analysis.get('file_references'):
                        print(f"        - File refs: {len(content_analysis.get('file_references', []))}")
                    
                    print(f"        - Text length: {content_analysis.get('text_length', 0)} chars")
        
        # Thống kê tổng quan
        print(f"\n📊 Thống kê phân loại:")
        print(f"   👤 User messages: {user_messages}")
        print(f"   🤖 Manus messages: {manus_messages}")
        print(f"   📋 Message types breakdown:")
        
        for msg_type, count in message_types.items():
            percentage = (count / manus_messages * 100) if manus_messages > 0 else 0
            print(f"      - {msg_type}: {count} ({percentage:.1f}%)")
        
        # Kiểm tra xem có message nào được phân loại không
        if manus_messages > 0 and len(message_types) > 0:
            print(f"\n✅ Message classification hoạt động tốt!")
            print(f"   - Đã phân loại {manus_messages} Manus messages")
            print(f"   - Phát hiện {len(message_types)} loại message khác nhau")
            return True
        else:
            print(f"\n⚠️  Không có message nào được phân loại!")
            return False
            
    except Exception as e:
        print(f"❌ Lỗi khi test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def test_message_classification_edge_cases():
    """Test các trường hợp đặc biệt của message classification."""
    print("\n🧪 Testing edge cases for message classification...")
    
    test_cases = [
        {
            "name": "Empty message",
            "html": "",
            "text": "",
            "expected_type": "empty"
        },
        {
            "name": "HTML only",
            "html": "<div class='prose'><p>Test message</p></div>",
            "text": "",
            "expected_type": "text"
        },
        {
            "name": "Text only",
            "html": "",
            "text": "Simple text message without HTML",
            "expected_type": "text"
        },
        {
            "name": "Malformed HTML",
            "html": "<div><p>Unclosed tags<div><span>",
            "text": "Malformed HTML content",
            "expected_type": "text"
        }
    ]
    
    from app.core.message_classifier import ManusMessageClassifier
    
    passed = 0
    failed = 0
    
    for case in test_cases:
        try:
            print(f"\n   Testing: {case['name']}")
            result = ManusMessageClassifier.classify_message_type(
                manus_html=case['html'],
                manus_text=case['text']
            )
            
            actual_type = result.get('message_subtype', 'unknown')
            expected_type = case['expected_type']
            
            print(f"     Expected: {expected_type}")
            print(f"     Actual: {actual_type}")
            print(f"     Confidence: {result.get('confidence', 0)}")
            
            if actual_type == expected_type:
                print(f"     ✅ Passed")
                passed += 1
            else:
                print(f"     ❌ Failed - Expected {expected_type}, got {actual_type}")
                failed += 1
                
        except Exception as e:
            print(f"     ❌ Error: {str(e)}")
            failed += 1
    
    print(f"\n   Edge case results: {passed} passed, {failed} failed")
    return failed == 0


async def main():
    """Chạy tất cả tests."""
    print("🚀 Starting Real HTML Message Classification Tests...\n")
    
    # Test với dữ liệu HTML thực
    test1_success = await test_real_html_classification()
    
    # Test edge cases
    test2_success = await test_message_classification_edge_cases()
    
    print(f"\n📊 Final Results:")
    if test1_success and test2_success:
        print("🎉 All tests passed! Message classification system is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
